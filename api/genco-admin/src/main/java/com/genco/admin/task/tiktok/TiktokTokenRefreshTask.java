package com.genco.admin.task.tiktok;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.genco.common.constants.Constants;
import com.genco.common.constants.SysConfigConstants;
import com.genco.common.exception.CrmebException;
import com.genco.common.token.TikTokOauthToken;
import com.genco.common.utils.DateUtil;
import com.genco.service.service.SystemConfigService;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 定时任务刷新tiktok的token
 */
@Component
@Configuration //读取配置
@EnableScheduling // 开启定时任务
public class TiktokTokenRefreshTask {

    // 日志
    private static final Logger logger = LoggerFactory.getLogger(TiktokTokenRefreshTask.class);
    private static final int MAX_RETRY = 3;
    private static final int RETRY_INTERVAL_MS = 60_000; // 1分钟

    @Autowired
    private SystemConfigService systemConfigService;


    @Scheduled(fixedDelay = 1000 * 60 * 10L) // 每10分钟检查一次
    public void refreshTiktokToken() {
        logger.info("---TiktokTokenRefreshTask task------produce Data with fixed rate task: Execution Time - {}", DateUtil.nowDateTime());
        try {
            Integer nowTimeStamp = DateUtil.getNowTime();
            Integer accessExpireAt = Integer.valueOf(systemConfigService.getValueByKey("tiktok_access_token_expire_in")); // 绝对时间戳
            // 提前5分钟刷新
            if (accessExpireAt - nowTimeStamp < 300) {
                boolean success = false;
                Exception lastException = null;
                for (int attempt = 1; attempt <= MAX_RETRY; attempt++) {
                    try {
                        success = doRefresh(nowTimeStamp);
                        if (success) break;
                    } catch (Exception e) {
                        lastException = e;
                        logger.error("TiktokTokenRefreshTask: 刷新第{}次失败: {}", attempt, e.getMessage(), e);
                    }
                    if (attempt < MAX_RETRY) {
                        try {
                            Thread.sleep(RETRY_INTERVAL_MS);
                        } catch (InterruptedException ignored) {
                        }
                    }
                }
                if (!success) {
                    String alarmMsg = "Tiktok access_token 刷新连续失败" + (lastException != null ? (": " + lastException.getMessage()) : "");
                    sendAlarm(alarmMsg);
                }
            } else {
                logger.info("Tiktok access_token 未到刷新时间，当前时间: {}，过期时间: {}", nowTimeStamp, accessExpireAt);
            }
        } catch (Exception e) {
            logger.error("TiktokTokenRefreshTask.task | msg : {}", e.getMessage(), e);
            sendAlarm("TiktokTokenRefreshTask.task 执行异常: " + e.getMessage());
        }
    }

    /**
     * 实际刷新逻辑，成功返回true，失败抛异常或返回false
     */
    protected boolean doRefresh(Integer nowTimeStamp) {
        String app_key = systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_APP_KEY);
        if (StrUtil.isBlank(app_key)) {
            throw new CrmebException("TikTok appId未设置");
        }
        String app_secret = systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_APP_SECRET);
        if (StrUtil.isBlank(app_secret)) {
            throw new CrmebException("TikTok secret未设置");
        }
        String refresh_token = systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_TOKEN_REFRESH_CODE);
        TikTokOauthToken tikTokOauthToken;

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(Constants.TIKTOK_ACCESS_TOKEN_REFRESH_URL);
            httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded");
            httpPost.setHeader("Cache-Control", "no-cache");

            List<NameValuePair> params = new ArrayList<>();
            params.add(new BasicNameValuePair("app_key", app_key));
            params.add(new BasicNameValuePair("app_secret", app_secret));
            params.add(new BasicNameValuePair("refresh_token", refresh_token));
            params.add(new BasicNameValuePair("grant_type", "refresh_token"));

            String formData = params.stream()
                    .map(p -> p.getName() + "=" + p.getValue())
                    .collect(Collectors.joining("&"));

            httpPost.setEntity(new StringEntity(formData));

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                String responseBody = EntityUtils.toString(response.getEntity());
                if (ObjectUtil.isNull(responseBody)) {
                    throw new CrmebException("平台接口异常，没任何数据返回！");
                }
                tikTokOauthToken = JSONObject.parseObject(responseBody, TikTokOauthToken.class);
            }
            if (tikTokOauthToken != null && StringUtils.isNoneBlank(tikTokOauthToken.getAccessToken())) {
                systemConfigService.updateOrSaveValueByName(SysConfigConstants.TIKTOK_TOKEN_REFRESH_CODE, tikTokOauthToken.getRefreshToken());
                systemConfigService.updateOrSaveValueByName(SysConfigConstants.TIKTOK_ACCESS_TOKEN, tikTokOauthToken.getAccessToken());
                systemConfigService.updateOrSaveValueByName(SysConfigConstants.TIKTOK_TOKEN_EXPIRE_IN, tikTokOauthToken.getExpiresIn().toString());
                logger.info("Tiktok access_token refreshed successfully, new expire at: {}", tikTokOauthToken.getExpiresIn());
                return true;
            } else {
                logger.error("TiktokTokenRefreshTask: 刷新失败，返回内容: {}", tikTokOauthToken);
                return false;
            }
        } catch (Exception e) {
            logger.error("TiktokTokenRefreshTask: 刷新请求异常: {}", e.getMessage(), e);
            throw new CrmebException("TiktokTokenRefreshTask: 刷新请求异常: " + e.getMessage());
        }
    }

    /**
     * 失败告警方法（可对接邮件、钉钉、短信等）
     */
    protected void sendAlarm(String message) {
        // TODO: 实现告警逻辑，如邮件、钉钉、短信等
        logger.error("【TiktokTokenRefreshTask告警】{}", message);
    }
}
