package com.genco.admin.controller;

import com.genco.common.model.affiliate.AffiliateProductHistory;
import com.genco.common.page.CommonPage;
import com.genco.common.request.AffiliateProductImportRequest;
import com.genco.common.request.AffiliateProductSearchRequest;
import com.genco.common.request.PageParamRequest;
import com.genco.common.response.AffiliateProductImportResponse;
import com.genco.common.response.AffiliateProductResponse;
import com.genco.common.response.CommonResult;
import com.genco.service.service.AffiliateProductHistoryService;
import com.genco.service.service.AffiliateProductService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 联盟产品控制器
 */
@Slf4j
@RestController
@RequestMapping("api/admin/affiliate/products")
@Api(tags = "运营中心 - 联盟产品")
public class AffiliateProductController {

    @Autowired
    private AffiliateProductService affiliateProductService;

    @Autowired
    private AffiliateProductHistoryService affiliateProductHistoryService;

    /**
     * 搜索联盟产品
     */
    @ApiOperation(value = "搜索联盟产品", notes = "基于TikTok Shop API的联盟产品搜索功能")
    @PostMapping("/search")
    public CommonResult<AffiliateProductResponse> searchProducts(@RequestBody @Validated AffiliateProductSearchRequest request) {

        log.info("联盟产品搜索请求 - 页面大小: {}, 关键词: {}, 类目: {}",
                request.getPageSize(), request.getTitleKeywords(), request.getCategoryId());

        try {
            // 调用Service层进行搜索
            log.info("调用AffiliateProductService进行联盟产品搜索");
            AffiliateProductResponse response = affiliateProductService.searchProducts(request);

            log.info("联盟产品搜索成功，返回产品数量: {}",
                response.getProducts() != null ? response.getProducts().size() : 0);

            // 返回搜索结果
            return CommonResult.success(response, "联盟产品搜索成功");

        } catch (Exception e) {
            log.error("联盟产品搜索失败", e);

            // 根据异常类型返回不同的错误信息
            String errorMessage = e.getMessage();
            if (errorMessage != null) {
                if (errorMessage.contains("timeout") || errorMessage.contains("timed out")) {
                    return CommonResult.failed("TikTok API请求超时，请稍后重试");
                } else if (errorMessage.contains("401") || errorMessage.contains("Unauthorized")) {
                    return CommonResult.failed("TikTok API认证失败，请检查访问令牌");
                } else if (errorMessage.contains("403") || errorMessage.contains("Forbidden")) {
                    return CommonResult.failed("TikTok API权限不足，请检查应用权限配置");
                } else if (errorMessage.contains("429") || errorMessage.contains("rate limit")) {
                    return CommonResult.failed("TikTok API调用频率超限，请稍后重试");
                } else if (errorMessage.contains("配置")) {
                    return CommonResult.failed("TikTok API配置错误: " + errorMessage);
                }
            }

            return CommonResult.failed("联盟产品搜索失败: " + errorMessage);
        }
    }

    /**
     * 批量导入联盟商品到本地数据库
     */
    @ApiOperation(value = "批量导入联盟商品", notes = "将选中的TikTok联盟商品导入到本地数据库")
    @PostMapping("/import")
    public CommonResult<AffiliateProductImportResponse> importProducts(@RequestBody @Validated AffiliateProductImportRequest request) {
        log.info("开始批量导入联盟商品，商品数量：{}（品牌信息将自动从TikTok API提取）", request.getProductIds().size());

        try {
            AffiliateProductImportResponse response = affiliateProductHistoryService.importProducts(request);
            log.info("批量导入完成，总数：{}，成功：{}，失败：{}，跳过：{}",
                    response.getTotalCount(), response.getSuccessCount(), response.getFailedCount(), response.getSkippedCount());

            return CommonResult.success(response, "商品导入完成");
        } catch (Exception e) {
            log.error("批量导入联盟商品失败", e);
            return CommonResult.failed("商品导入失败: " + e.getMessage());
        }
    }

    /**
     * 标记商品为已删除状态
     */
    @ApiOperation(value = "删除联盟商品", notes = "标记选中的联盟商品为已删除状态")
    @PostMapping("/delete")
    public CommonResult<Boolean> deleteProducts(@RequestBody List<String> productIds) {
        log.info("开始删除联盟商品，商品数量：{}", productIds.size());

        try {
            // TODO: 从当前登录用户获取操作用户信息
            String operationUser = "admin"; // 临时硬编码，实际应该从SecurityContext获取

            Boolean result = affiliateProductHistoryService.markProductsAsDeleted(productIds, operationUser);

            if (result) {
                log.info("删除联盟商品成功，商品数量：{}", productIds.size());
                return CommonResult.success(result, "商品删除成功");
            } else {
                return CommonResult.failed("商品删除失败");
            }
        } catch (Exception e) {
            log.error("删除联盟商品失败", e);
            return CommonResult.failed("商品删除失败: " + e.getMessage());
        }
    }

    /**
     * 查询商品历史记录
     */
    @ApiOperation(value = "查询商品历史记录", notes = "分页查询联盟商品的操作历史记录")
    @GetMapping("/history")
    public CommonResult<CommonPage<AffiliateProductHistory>> getProductHistory(
            @RequestParam(value = "status", required = false) Integer status,
            @RequestParam(value = "brandId", required = false) Integer brandId,
            @Validated PageParamRequest pageParamRequest) {

        log.info("查询商品历史记录，状态：{}，品牌ID：{}", status, brandId);

        try {
            CommonPage<AffiliateProductHistory> result = affiliateProductHistoryService.getHistoryList(status, brandId, pageParamRequest);
            return CommonResult.success(result, "查询成功");
        } catch (Exception e) {
            log.error("查询商品历史记录失败", e);
            return CommonResult.failed("查询失败: " + e.getMessage());
        }
    }

    /**
     * 检查商品是否已存在
     */
    @ApiOperation(value = "检查商品是否已存在", notes = "检查指定的商品ID是否已经在历史记录中存在")
    @PostMapping("/check-existing")
    public CommonResult<List<String>> checkExistingProducts(@RequestBody List<String> productIds) {
        log.info("检查商品是否已存在，商品数量：{}", productIds.size());

        try {
            List<String> existingProductIds = affiliateProductHistoryService.checkExistingProducts(productIds);
            log.info("检查完成，已存在商品数量：{}", existingProductIds.size());

            return CommonResult.success(existingProductIds, "检查完成");
        } catch (Exception e) {
            log.error("检查商品是否已存在失败", e);
            return CommonResult.failed("检查失败: " + e.getMessage());
        }
    }
}
