package com.genco.admin.controller;

import com.genco.common.model.product.StoreProductShareRecord;
import com.genco.common.page.CommonPage;
import com.genco.common.request.PageParamRequest;
import com.genco.common.request.StoreProductShareRecordSearchRequest;
import com.genco.common.response.CommonResult;
import com.genco.service.service.StoreProductShareRecordService;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("api/admin/store/productShareRecord")
@Api(tags = "商品分享转链记录")
public class StoreProductShareRecordController {

    @Autowired
    private StoreProductShareRecordService storeProductShareRecordService;

    @ApiOperation(value = "分页查询商品分享转链记录")
    @GetMapping("/list")
    public CommonResult<CommonPage<StoreProductShareRecord>> getList(
            StoreProductShareRecordSearchRequest request,
            PageParamRequest pageParamRequest) {
        PageInfo<StoreProductShareRecord> pageInfo = storeProductShareRecordService.getAdminList(request, pageParamRequest);
        return CommonResult.success(CommonPage.restPage(pageInfo));
    }
} 