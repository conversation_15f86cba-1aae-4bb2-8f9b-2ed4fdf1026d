package com.genco.service.service;

import com.genco.common.request.RewardStatisticsRequest;
import com.genco.common.response.RewardStatisticsResponse;
import com.github.pagehelper.PageInfo;

/**
 * 奖励统计服务接口
 */
public interface RewardStatisticsService {

    /**
     * 获取奖励统计数据
     *
     * @param request 查询请求参数
     * @return RewardStatisticsResponse
     */
    RewardStatisticsResponse getRewardStatistics(RewardStatisticsRequest request);

    /**
     * 获取奖励发放明细列表（分页）
     *
     * @param request 查询请求参数
     * @return PageInfo<RewardStatisticsResponse.RewardDetailItem>
     */
    PageInfo<RewardStatisticsResponse.RewardDetailItem> getRewardDetailList(RewardStatisticsRequest request);

    /**
     * 导出奖励发放明细
     *
     * @param request 查询请求参数
     * @return 导出文件路径
     */
    String exportRewardDetails(RewardStatisticsRequest request);
}
