package com.genco.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.genco.common.model.affiliate.AffiliateProductHistory;
import com.genco.common.model.product.StoreProduct;
import com.genco.common.page.CommonPage;
import com.genco.common.request.AffiliateProductImportRequest;
import com.genco.common.request.PageParamRequest;
import com.genco.common.response.AffiliateProductImportResponse;
import com.genco.service.dao.AffiliateProductHistoryDao;
import com.genco.service.service.AffiliateProductHistoryService;
import com.genco.service.service.ProductImportService;
import com.github.pagehelper.PageHelper;
import tiktokshop.open.sdk_java.model.AffiliateCreator.V202501.CreatorSelectAffiliateProductResponseDataProducts;
import tiktokshop.open.sdk_java.model.AffiliateCreator.V202405.CreatorSearchOpenCollaborationProductResponseDataProducts;
import tiktokshop.open.sdk_java.model.AffiliateCreator.V202405.CreatorSearchOpenCollaborationProductResponseDataProductsCategoryChains;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 联盟商品历史记录服务实现
 */
@Slf4j
@Service
public class AffiliateProductHistoryServiceImpl extends ServiceImpl<AffiliateProductHistoryDao, AffiliateProductHistory>
        implements AffiliateProductHistoryService {

    @Autowired
    private ProductImportService productImportService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AffiliateProductImportResponse importProducts(AffiliateProductImportRequest request) {
        log.info("开始批量导入联盟商品，商品数量：{}", request.getProductIds().size());

        AffiliateProductImportResponse response = new AffiliateProductImportResponse();
        response.setTotalCount(request.getProductIds().size());
        response.setSuccessCount(0);
        response.setFailedCount(0);
        response.setSkippedCount(0);
        response.setSuccessProductIds(new ArrayList<>());
        response.setFailedProducts(new ArrayList<>());

        // 检查已入库的商品（状态为1的商品）
        List<String> alreadyImportedProductIds = checkAlreadyImportedProducts(request.getProductIds());
        response.setSkippedCount(alreadyImportedProductIds.size());

        // 过滤出需要导入的商品ID
        List<String> toImportProductIds = request.getProductIds().stream()
                .filter(id -> !alreadyImportedProductIds.contains(id))
                .collect(Collectors.toList());

        if (toImportProductIds.isEmpty()) {
            log.info("所有商品都已入库，无需导入");
            return response;
        }

        // 使用ProductImportService进行批量导入
        for (String productId : toImportProductIds) {
            try {
                // 调用现有的ProductImportService导入单个商品
                List<StoreProduct> importedProducts = productImportService.importProductsFromTikTok(productId);

                if (!importedProducts.isEmpty()) {
                    StoreProduct importedProduct = importedProducts.get(0);

                    // 更新AffiliateProductHistory表的入库状态
                    Boolean updateResult = updateProductImportStatus(productId, request.getOperationUser());

                    if (updateResult) {
                        response.setSuccessCount(response.getSuccessCount() + 1);
                        response.getSuccessProductIds().add(productId);
                        log.info("商品导入成功，TikTok商品ID：{}，本地商品ID：{}", productId, importedProduct.getId());
                    } else {
                        response.setFailedCount(response.getFailedCount() + 1);
                        AffiliateProductImportResponse.FailedProductInfo failedInfo = new AffiliateProductImportResponse.FailedProductInfo();
                        failedInfo.setProductId(productId);
                        failedInfo.setErrorMessage("更新历史记录状态失败");
                        response.getFailedProducts().add(failedInfo);
                    }
                } else {
                    response.setFailedCount(response.getFailedCount() + 1);
                    AffiliateProductImportResponse.FailedProductInfo failedInfo = new AffiliateProductImportResponse.FailedProductInfo();
                    failedInfo.setProductId(productId);
                    failedInfo.setErrorMessage("导入到本地数据库失败");
                    response.getFailedProducts().add(failedInfo);
                }
            } catch (Exception e) {
                log.error("导入商品失败，商品ID：{}", productId, e);
                response.setFailedCount(response.getFailedCount() + 1);
                AffiliateProductImportResponse.FailedProductInfo failedInfo = new AffiliateProductImportResponse.FailedProductInfo();
                failedInfo.setProductId(productId);
                failedInfo.setErrorMessage(e.getMessage());
                response.getFailedProducts().add(failedInfo);
            }
        }

        log.info("批量导入完成，总数：{}，成功：{}，失败：{}，跳过：{}",
                response.getTotalCount(), response.getSuccessCount(), response.getFailedCount(), response.getSkippedCount());

        return response;
    }


    @Override
    public Boolean markProductsAsDeleted(List<String> productIds, String operationUser) {
        if (CollectionUtils.isEmpty(productIds)) {
            return false;
        }

        try {
            // 批量更新删除状态
            boolean allSuccess = true;
            for (String productId : productIds) {
                Boolean result = updateProductDeleteStatus(productId, operationUser);
                if (!result) {
                    allSuccess = false;
                    log.error("更新商品删除状态失败，商品ID：{}", productId);
                }
            }

            return allSuccess;
        } catch (Exception e) {
            log.error("标记商品为已删除失败", e);
            return false;
        }
    }

    @Override
    public AffiliateProductHistory getByProductId(String productId) {
        LambdaQueryWrapper<AffiliateProductHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AffiliateProductHistory::getProductId, productId);
        return getOne(queryWrapper);
    }

    @Override
    public CommonPage<AffiliateProductHistory> getHistoryList(Integer status, Integer brandId, PageParamRequest pageParamRequest) {
        LambdaQueryWrapper<AffiliateProductHistory> queryWrapper = new LambdaQueryWrapper<>();

        if (status != null) {
            queryWrapper.eq(AffiliateProductHistory::getStatus, status);
        }

        queryWrapper.orderByDesc(AffiliateProductHistory::getOperationTime);

        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        List<AffiliateProductHistory> list = list(queryWrapper);

        return CommonPage.restPage(list);
    }

    @Override
    public List<String> checkExistingProducts(List<String> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<AffiliateProductHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AffiliateProductHistory::getProductId, productIds);
        queryWrapper.select(AffiliateProductHistory::getProductId);

        List<AffiliateProductHistory> existingHistories = list(queryWrapper);

        return existingHistories.stream()
                .map(AffiliateProductHistory::getProductId)
                .collect(Collectors.toList());
    }

    /**
     * 检查已入库的商品（状态为1的商品）
     */
    private List<String> checkAlreadyImportedProducts(List<String> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<AffiliateProductHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AffiliateProductHistory::getProductId, productIds);
        queryWrapper.eq(AffiliateProductHistory::getStatus, 1); // 已入库状态
        queryWrapper.select(AffiliateProductHistory::getProductId);

        List<AffiliateProductHistory> importedHistories = list(queryWrapper);

        return importedHistories.stream()
                .map(AffiliateProductHistory::getProductId)
                .collect(Collectors.toList());
    }

    @Override
    public void createProductHistoryFromTikTokData(CreatorSearchOpenCollaborationProductResponseDataProducts tikTokProduct) {
        try {
            AffiliateProductHistory history = new AffiliateProductHistory();

            // 基本信息
            history.setProductId(tikTokProduct.getId());
            history.setTitle(tikTokProduct.getTitle());
            history.setMainImageUrl(tikTokProduct.getMainImageUrl());
            history.setDetailLink(tikTokProduct.getDetailLink());
            history.setSaleRegion(tikTokProduct.getSaleRegion());
            history.setHasInventory(tikTokProduct.getHasInventory());

            // 安全转换Long到Integer
            if (tikTokProduct.getUnitsSold() != null) {
                history.setUnitsSold(tikTokProduct.getUnitsSold().intValue());
            }

            // 价格信息
            if (tikTokProduct.getOriginalPrice() != null) {
                history.setOriginalPriceMin(tikTokProduct.getOriginalPrice().getMinimumAmount());
                history.setOriginalPriceMax(tikTokProduct.getOriginalPrice().getMaximumAmount());
                history.setOriginalPriceCurrency(tikTokProduct.getOriginalPrice().getCurrency());
            }

            if (tikTokProduct.getSalesPrice() != null) {
                history.setSalesPriceMin(tikTokProduct.getSalesPrice().getMinimumAmount());
                history.setSalesPriceMax(tikTokProduct.getSalesPrice().getMaximumAmount());
                history.setSalesPriceCurrency(tikTokProduct.getSalesPrice().getCurrency());
            }

            // 佣金信息
            if (tikTokProduct.getCommission() != null) {
                // 安全转换Long到Integer
                if (tikTokProduct.getCommission().getRate() != null) {
                    history.setCommissionRate(tikTokProduct.getCommission().getRate().intValue());
                }
                history.setCommissionAmount(tikTokProduct.getCommission().getAmount());
                history.setCommissionCurrency(tikTokProduct.getCommission().getCurrency());
            }

            // 分类信息（取第一个分类）
            if (tikTokProduct.getCategoryChains() != null && !tikTokProduct.getCategoryChains().isEmpty()) {
                CreatorSearchOpenCollaborationProductResponseDataProductsCategoryChains firstCategory = tikTokProduct.getCategoryChains().get(0);
                history.setCategoryId(firstCategory.getId());
                history.setCategoryName(firstCategory.getLocalName()); // 使用getLocalName()
                history.setCategoryIsLeaf(firstCategory.getIsLeaf());
                history.setCategoryParentId(firstCategory.getParentId());
            }

            // 店铺信息
            if (tikTokProduct.getShop() != null) {
                history.setShopName(tikTokProduct.getShop().getName());
            }

            // 设置状态为未入库
            history.setStatus(0);
            history.setOperationType("SYNC");
            history.setOperationTime(new Date());
            history.setCreateTime(new Date());
            history.setUpdateTime(new Date());

            this.save(history);
            log.debug("创建TikTok商品历史记录成功，商品ID：{}", tikTokProduct.getId());
        } catch (Exception e) {
            log.error("创建TikTok商品历史记录失败，商品ID：{}", tikTokProduct.getId(), e);
            throw e;
        }
    }

    @Override
    public void updateProductHistoryFromTikTokData(CreatorSearchOpenCollaborationProductResponseDataProducts tikTokProduct) {
        try {
            AffiliateProductHistory existingHistory = getByProductId(tikTokProduct.getId());
            if (existingHistory == null) {
                log.warn("未找到商品历史记录，商品ID：{}", tikTokProduct.getId());
                return;
            }

            // 更新TikTok商品数据
            existingHistory.setTitle(tikTokProduct.getTitle());
            existingHistory.setMainImageUrl(tikTokProduct.getMainImageUrl());
            existingHistory.setDetailLink(tikTokProduct.getDetailLink());
            existingHistory.setSaleRegion(tikTokProduct.getSaleRegion());
            existingHistory.setHasInventory(tikTokProduct.getHasInventory());

            // 安全转换Long到Integer
            if (tikTokProduct.getUnitsSold() != null) {
                existingHistory.setUnitsSold(tikTokProduct.getUnitsSold().intValue());
            }

            // 价格信息
            if (tikTokProduct.getOriginalPrice() != null) {
                existingHistory.setOriginalPriceMin(tikTokProduct.getOriginalPrice().getMinimumAmount());
                existingHistory.setOriginalPriceMax(tikTokProduct.getOriginalPrice().getMaximumAmount());
                existingHistory.setOriginalPriceCurrency(tikTokProduct.getOriginalPrice().getCurrency());
            }

            if (tikTokProduct.getSalesPrice() != null) {
                existingHistory.setSalesPriceMin(tikTokProduct.getSalesPrice().getMinimumAmount());
                existingHistory.setSalesPriceMax(tikTokProduct.getSalesPrice().getMaximumAmount());
                existingHistory.setSalesPriceCurrency(tikTokProduct.getSalesPrice().getCurrency());
            }

            // 佣金信息
            if (tikTokProduct.getCommission() != null) {
                // 安全转换Long到Integer
                if (tikTokProduct.getCommission().getRate() != null) {
                    existingHistory.setCommissionRate(tikTokProduct.getCommission().getRate().intValue());
                }
                existingHistory.setCommissionAmount(tikTokProduct.getCommission().getAmount());
                existingHistory.setCommissionCurrency(tikTokProduct.getCommission().getCurrency());
            }

            // 分类信息（取第一个分类）
            if (tikTokProduct.getCategoryChains() != null && !tikTokProduct.getCategoryChains().isEmpty()) {
                CreatorSearchOpenCollaborationProductResponseDataProductsCategoryChains firstCategory = tikTokProduct.getCategoryChains().get(0);
                existingHistory.setCategoryId(firstCategory.getId());
                existingHistory.setCategoryName(firstCategory.getLocalName()); // 使用getLocalName()
                existingHistory.setCategoryIsLeaf(firstCategory.getIsLeaf());
                existingHistory.setCategoryParentId(firstCategory.getParentId());
            }

            // 店铺信息
            if (tikTokProduct.getShop() != null) {
                existingHistory.setShopName(tikTokProduct.getShop().getName());
            }

            existingHistory.setUpdateTime(new Date());

            this.updateById(existingHistory);
            log.debug("更新TikTok商品历史记录成功，商品ID：{}", tikTokProduct.getId());
        } catch (Exception e) {
            log.error("更新TikTok商品历史记录失败，商品ID：{}", tikTokProduct.getId(), e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateProductImportStatus(String productId, String operationUser) {
        try {
            LambdaUpdateWrapper<AffiliateProductHistory> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(AffiliateProductHistory::getProductId, productId)
                    .set(AffiliateProductHistory::getStatus, 1) // 已入库
                    .set(AffiliateProductHistory::getOperationType, "IMPORT")
                    .set(AffiliateProductHistory::getOperationUser, operationUser)
                    .set(AffiliateProductHistory::getOperationTime, new Date())
                    .set(AffiliateProductHistory::getImportTime, new Date())
                    .set(AffiliateProductHistory::getUpdateTime, new Date());

            boolean result = update(updateWrapper);
            log.info("更新商品入库状态：{}，商品ID：{}", result ? "成功" : "失败", productId);
            return result;
        } catch (Exception e) {
            log.error("更新商品入库状态失败，商品ID：{}，错误：{}", productId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateProductDeleteStatus(String productId, String operationUser) {
        try {
            LambdaUpdateWrapper<AffiliateProductHistory> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(AffiliateProductHistory::getProductId, productId)
                    .set(AffiliateProductHistory::getStatus, 2) // 已删除
                    .set(AffiliateProductHistory::getOperationType, "DELETE")
                    .set(AffiliateProductHistory::getOperationUser, operationUser)
                    .set(AffiliateProductHistory::getOperationTime, new Date())
                    .set(AffiliateProductHistory::getDeleteTime, new Date())
                    .set(AffiliateProductHistory::getUpdateTime, new Date());

            boolean result = update(updateWrapper);
            log.info("更新商品删除状态：{}，商品ID：{}", result ? "成功" : "失败", productId);
            return result;
        } catch (Exception e) {
            log.error("更新商品删除状态失败，商品ID：{}，错误：{}", productId, e.getMessage(), e);
            return false;
        }
    }
}
