package com.genco.service;

import com.genco.common.request.PageParamRequest;
import com.genco.common.request.StoreProductShareRecordSearchRequest;
import com.genco.service.service.StoreProductShareRecordService;
import com.github.pagehelper.PageInfo;
import com.genco.common.model.product.StoreProductShareRecord;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 商品分享转链记录服务测试类
 * 用于验证SQL注入修复后的功能
 */
@SpringBootTest
public class StoreProductShareRecordServiceTest {

    @Autowired
    private StoreProductShareRecordService storeProductShareRecordService;

    /**
     * 测试品牌筛选功能（修复SQL注入后）
     */
    @Test
    public void testBrandCodeFilterSafe() {
        StoreProductShareRecordSearchRequest request = new StoreProductShareRecordSearchRequest();
        // 测试正常的品牌代码
        request.setBrandCode("BRAND001");
        
        PageParamRequest pageRequest = new PageParamRequest();
        pageRequest.setPage(1);
        pageRequest.setLimit(10);
        
        PageInfo<StoreProductShareRecord> result = storeProductShareRecordService.getAdminList(request, pageRequest);
        
        System.out.println("正常品牌筛选结果数量: " + result.getList().size());
        System.out.println("总记录数: " + result.getTotal());
    }

    /**
     * 测试SQL注入防护
     */
    @Test
    public void testSqlInjectionProtection() {
        StoreProductShareRecordSearchRequest request = new StoreProductShareRecordSearchRequest();
        // 尝试SQL注入攻击
        request.setBrandCode("'; DROP TABLE eb_store_product; --");
        
        PageParamRequest pageRequest = new PageParamRequest();
        pageRequest.setPage(1);
        pageRequest.setLimit(10);
        
        try {
            PageInfo<StoreProductShareRecord> result = storeProductShareRecordService.getAdminList(request, pageRequest);
            System.out.println("SQL注入测试通过，返回结果数量: " + result.getList().size());
        } catch (Exception e) {
            System.out.println("SQL注入测试异常: " + e.getMessage());
        }
    }

    /**
     * 测试不存在的品牌代码
     */
    @Test
    public void testNonExistentBrandCode() {
        StoreProductShareRecordSearchRequest request = new StoreProductShareRecordSearchRequest();
        request.setBrandCode("NON_EXISTENT_BRAND");
        
        PageParamRequest pageRequest = new PageParamRequest();
        pageRequest.setPage(1);
        pageRequest.setLimit(10);
        
        PageInfo<StoreProductShareRecord> result = storeProductShareRecordService.getAdminList(request, pageRequest);
        
        System.out.println("不存在品牌筛选结果数量: " + result.getList().size());
        System.out.println("总记录数: " + result.getTotal());
    }

    /**
     * 测试关键词搜索功能
     */
    @Test
    public void testKeywordSearch() {
        StoreProductShareRecordSearchRequest request = new StoreProductShareRecordSearchRequest();
        request.setKeyword("商品");
        
        PageParamRequest pageRequest = new PageParamRequest();
        pageRequest.setPage(1);
        pageRequest.setLimit(10);
        
        PageInfo<StoreProductShareRecord> result = storeProductShareRecordService.getAdminList(request, pageRequest);
        
        System.out.println("关键词搜索结果数量: " + result.getList().size());
        System.out.println("总记录数: " + result.getTotal());
    }

    /**
     * 测试组合搜索功能
     */
    @Test
    public void testCombinedSearch() {
        StoreProductShareRecordSearchRequest request = new StoreProductShareRecordSearchRequest();
        request.setKeyword("商品");
        request.setBrandCode("BRAND001");
        
        PageParamRequest pageRequest = new PageParamRequest();
        pageRequest.setPage(1);
        pageRequest.setLimit(10);
        
        PageInfo<StoreProductShareRecord> result = storeProductShareRecordService.getAdminList(request, pageRequest);
        
        System.out.println("组合搜索结果数量: " + result.getList().size());
        System.out.println("总记录数: " + result.getTotal());
    }
}
