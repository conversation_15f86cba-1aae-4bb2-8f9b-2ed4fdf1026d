# 会员等级制度升级实施文档

## 📋 项目概述

本次升级将现有的基于经验值的会员等级制度改为基于佣金比例的新等级制度，支持付费购买和线下申请等多种升级方式。

## 🎯 新等级体系

| 等级名称 | 等级编号 | 佣金比例 | 升级条件 | 升级费用 | 当前状态 |
|---------|---------|---------|---------|---------|---------|
| 普通用户 | 1 | 80% | 注册即可 | 免费 | ✅ 已开放 |
| 银牌用户 | 2 | 95% | 付费购买 | Rp 10,000 | ✅ 已开放 |
| 金牌用户 | 3 | 110% | 线下申请 | 免费 | 🔄 预留接口 |
| 钻石用户 | 4 | 125% | 线下申请 | 免费 | 🔄 预留接口 |
| 王者用户 | 5 | 140% | 线下申请 | 免费 | 🔄 预留接口 |
| 总团用户 | 6 | 155% | 渠道合作 | 免费 | 🔄 预留接口 |

## 🗄️ 数据库变更

### 1. 新增表结构

#### eb_user_level_upgrade_order (等级升级订单表)
```sql
CREATE TABLE `eb_user_level_upgrade_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `order_no` varchar(64) NOT NULL COMMENT '订单号',
  `uid` int(11) NOT NULL COMMENT '用户ID',
  `from_level_id` int(11) NOT NULL COMMENT '原等级ID',
  `to_level_id` int(11) NOT NULL COMMENT '目标等级ID',
  `upgrade_price` decimal(10,2) NOT NULL COMMENT '升级费用',
  `payment_method` varchar(32) DEFAULT 'balance' COMMENT '支付方式',
  `order_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '订单状态',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`)
);
```

#### eb_user_level_upgrade_log (等级升级日志表)
```sql
CREATE TABLE `eb_user_level_upgrade_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `uid` int(11) NOT NULL COMMENT '用户ID',
  `from_level_id` int(11) NOT NULL COMMENT '原等级ID',
  `to_level_id` int(11) NOT NULL COMMENT '目标等级ID',
  `upgrade_type` tinyint(4) NOT NULL COMMENT '升级方式',
  `order_no` varchar(64) DEFAULT NULL COMMENT '关联订单号',
  `remark` varchar(500) DEFAULT NULL COMMENT '升级备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

### 2. 修改现有表结构

#### eb_system_user_level (系统等级表)
新增字段：
- `upgrade_type` - 升级方式
- `upgrade_price` - 升级费用
- `commission_rate` - 佣金比例
- `is_available` - 是否开放

#### eb_user_level (用户等级记录表)
新增字段：
- `apply_status` - 申请状态
- `apply_time` - 申请时间
- `approve_time` - 审核时间
- `approver_id` - 审核人ID
- `apply_remark` - 申请备注
- `upgrade_order_id` - 升级订单号

## 🔧 代码变更

### 1. 新增实体类
- `UserLevelUpgradeOrder` - 等级升级订单
- `UserLevelUpgradeLog` - 等级升级日志
- `UserLevelUpgradeRequest` - 升级请求DTO
- `UserLevelUpgradeResponse` - 升级响应DTO

### 2. 新增服务类
- `UserLevelUpgradeService` - 等级升级服务接口
- `UserLevelUpgradeServiceImpl` - 等级升级服务实现

### 3. 新增控制器
- `UserLevelController` - 前端等级相关接口
- `UserLevelUpgradeController` - 管理端升级管理接口

### 4. 修改现有类
- `SystemUserLevel` - 新增佣金相关字段
- `UserLevel` - 新增审核相关字段
- `SystemUserLevelRequest` - 新增请求字段
- `SystemUserLevelService` - 新增获取开放等级列表方法

## 🚀 部署步骤

### 1. 数据库升级
```bash
# 执行数据库升级脚本
mysql -u root -p genco_data < api/sql/upgrade_member_level_system.sql
mysql -u root -p genco_data < api/sql/create_level_upgrade_order_table.sql
```

### 2. 代码部署
1. 编译项目
2. 重启应用服务
3. 验证接口功能

### 3. 验证步骤
1. 检查等级配置是否正确
2. 测试银牌等级购买流程
3. 验证用户数据迁移结果
4. 确认佣金计算逻辑

## 📱 API接口

### 前端接口
- `GET /api/front/user/level/available` - 获取可用等级列表
- `POST /api/front/user/level/upgrade` - 购买等级升级
- `GET /api/front/user/level/check-upgrade/{toLevelId}` - 检查升级条件

### 管理端接口
- `GET /api/admin/user/level/upgrade/list` - 升级订单列表
- `POST /api/admin/user/level/upgrade/cancel/{orderNo}` - 取消升级订单

## ⚠️ 注意事项

1. **数据备份**：执行升级前务必备份数据库
2. **用户迁移**：所有现有用户将重置为普通用户
3. **余额检查**：购买银牌等级需要足够的账户余额
4. **权限配置**：需要配置相应的管理员权限
5. **测试验证**：建议先在测试环境验证完整流程

## 🔄 后续扩展

1. **线下申请功能**：实现金牌/钻石/王者等级的申请审核流程
2. **渠道合作功能**：实现总团用户的合作伙伴管理
3. **佣金计算优化**：根据新等级调整订单返佣逻辑
4. **移动端界面**：更新APP中的等级显示和购买界面

## 📞 技术支持

如有问题请联系开发团队进行技术支持。
