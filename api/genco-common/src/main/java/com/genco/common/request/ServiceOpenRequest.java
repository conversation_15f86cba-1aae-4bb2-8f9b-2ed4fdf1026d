package com.genco.common.request;

import com.genco.common.annotation.StringContains;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * 服务开通请求对象
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "ServiceOpenRequest对象", description = "服务开通请求对象")
public class ServiceOpenRequest {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "服务类型:sms,短信;copy,产品复制;expr_query,物流查询;expr_dump,电子面单", required = true)
    @NotBlank(message = "服务类型不能为空")
    @StringContains(limitValues = {"sms", "copy", "expr_query", "expr_dump"}, message = "未知的服务类型")
    private String type;

    @ApiModelProperty(value = "短信签名，短信开通必填")
    private String sign;

    @ApiModelProperty(value = "快递公司简称，电子面单开通必填")
    private String com;

    @ApiModelProperty(value = "快递公司模板Id、电子面单开通必填")
    private String tempId;

    @ApiModelProperty(value = "快递面单发货人姓名，电子面单开通必填")
    private String toName;

    @ApiModelProperty(value = "快递面单发货人电话，电子面单开通必填")
    private String toTel;

    @ApiModelProperty(value = "发货人详细地址，电子面单开通必填")
    private String toAddress;

    @ApiModelProperty(value = "电子面单打印机编号，电子面单开通必填")
    private String siid;

}
