package com.genco.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 用户等级升级请求
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "UserLevelUpgradeRequest对象", description = "用户等级升级请求")
public class UserLevelUpgradeRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "目标等级ID", required = true)
    @NotNull(message = "目标等级ID不能为空")
    private Integer toLevelId;

    @ApiModelProperty(value = "支付方式：balance-余额支付")
    private String paymentMethod = "balance";

    @ApiModelProperty(value = "申请备注")
    private String remark;
}
