package com.genco.common.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户拉新奖励配置响应对象
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "UserReferralRewardConfigResponse对象", description = "用户拉新奖励配置响应")
public class UserReferralRewardConfigResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "用户ID")
    private Integer uid;

    @ApiModelProperty(value = "拉新数量要求")
    private Integer referralCount;

    @ApiModelProperty(value = "首单数量要求")
    private Integer firstOrderCount;

    @ApiModelProperty(value = "奖励金额（印尼盾）")
    private BigDecimal rewardAmount;

    @ApiModelProperty(value = "奖励规则中文描述（来自平台配置）")
    private String rewardRuleZh;

    @ApiModelProperty(value = "奖励规则英文描述（来自平台配置）")
    private String rewardRuleEn;

    @ApiModelProperty(value = "奖励规则印尼语描述（来自平台配置）")
    private String rewardRuleId;

    @ApiModelProperty(value = "状态：0-禁用，1-启用")
    private Integer status;

    @ApiModelProperty(value = "配置来源：PLATFORM-平台级，SILVER_MEMBER-银级会员")
    private String configSource;

    @ApiModelProperty(value = "是否可编辑（只有银级会员可编辑自己的配置）")
    private Boolean editable;

    @ApiModelProperty(value = "用户余额（印尼盾）")
    private BigDecimal userBalance;

    @ApiModelProperty(value = "累计邀请人数")
    private Integer totalReferralCount;

    @ApiModelProperty(value = "已兑换邀请人数")
    private Integer redeemedReferralCount;

    @ApiModelProperty(value = "累计首单数")
    private Integer totalFirstOrderCount;

    @ApiModelProperty(value = "已兑换首单数")
    private Integer redeemedFirstOrderCount;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
