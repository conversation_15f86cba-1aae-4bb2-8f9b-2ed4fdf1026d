package com.genco.common.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 订单状态图片
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="SystemGroupDataOrderStatusPicResponse对象", description="订单状态图片")
public class SystemGroupDataOrderStatusPicResponse implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 订单状态
     * 用于标识订单的当前状态，如：待付款、待发货、已发货、已完成等
     */
    @ApiModelProperty(value = "订单状态")
    private int orderStatus;

    /**
     * 图片地址
     * 对应订单状态的图片URL地址，用于前端展示不同状态对应的图片
     */
    @ApiModelProperty(value = "图片地址")
    private String url;

}
