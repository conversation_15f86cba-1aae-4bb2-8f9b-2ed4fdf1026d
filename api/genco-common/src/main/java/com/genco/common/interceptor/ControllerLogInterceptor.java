package com.genco.common.interceptor;

import com.alibaba.fastjson.JSON;
import com.genco.common.utils.LoggerUtil;
import com.genco.common.utils.RequestUtil;
import com.genco.common.utils.UserUtil;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Controller层方法调用日志拦截器
 * 记录摘要日志(digest)和详情日志(detail)
 *
 * <AUTHOR>
 */
@Component
public class ControllerLogInterceptor implements HandlerInterceptor {

    private static final Logger logger = LoggerUtil.getLogger(ControllerLogInterceptor.class);

    // 线程本地变量，用于存储请求开始时间和traceId
    private static final ThreadLocal<Long> startTimeHolder = new ThreadLocal<>();
    private static final ThreadLocal<String> traceIdHolder = new ThreadLocal<>();

    @Autowired
    private UserUtil userUtil;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 只处理HandlerMethod类型的handler
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();
        Class<?> clazz = handlerMethod.getBeanType();

        // 生成traceId
        String traceId = UUID.randomUUID().toString().replace("-", "");
        traceIdHolder.set(traceId);

        // 记录开始时间
        startTimeHolder.set(System.currentTimeMillis());

        // 获取用户ID
        Integer userId = userUtil.getCurrentUserId();

        // 记录摘要日志 - 请求开始
        LoggerUtil.digestInfo(logger,
                "请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}",
                traceId, clazz.getSimpleName(), method.getName(), userId,
                request.getRequestURI(), request.getMethod());

        // 记录详情日志 - 请求详情
        Map<String, Object> detailInfo = new HashMap<>();
        detailInfo.put("traceId", traceId);
        detailInfo.put("className", clazz.getName());
        detailInfo.put("methodName", method.getName());
        detailInfo.put("userId", userId);
        detailInfo.put("uri", request.getRequestURI());
        detailInfo.put("method", request.getMethod());
        detailInfo.put("requestParams", RequestUtil.getRequestParamAndHeader());
        detailInfo.put("requestBody", "未记录");
        detailInfo.put("userAgent", request.getHeader("User-Agent"));
        detailInfo.put("clientIp", getClientIp(request));

        LoggerUtil.detailInfo(logger, "请求详情: {}", JSON.toJSONString(detailInfo));

        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        // postHandle阶段不记录日志，在afterCompletion中统一处理
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 只处理HandlerMethod类型的handler
        if (!(handler instanceof HandlerMethod)) {
            return;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();
        Class<?> clazz = handlerMethod.getBeanType();

        // 获取线程本地变量
        String traceId = traceIdHolder.get();
        Long startTime = startTimeHolder.get();

        // 计算响应时间
        long responseTime = 0;
        if (startTime != null) {
            responseTime = System.currentTimeMillis() - startTime;
        }

        // 获取用户ID
        Integer userId = userUtil.getCurrentUserId();

        // 判断是否成功
        boolean isSuccess = ex == null && response.getStatus() < 400;

        // 记录摘要日志 - 请求结束
        LoggerUtil.digestInfo(logger,
                "请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}",
                traceId, clazz.getSimpleName(), method.getName(), userId, isSuccess, responseTime, response.getStatus());

        // 记录详情日志 - 响应详情
        Map<String, Object> detailInfo = new HashMap<>();
        detailInfo.put("traceId", traceId);
        detailInfo.put("className", clazz.getName());
        detailInfo.put("methodName", method.getName());
        detailInfo.put("userId", userId);
        detailInfo.put("isSuccess", isSuccess);
        detailInfo.put("responseTime", responseTime);
        detailInfo.put("statusCode", response.getStatus());
        detailInfo.put("requestBody", "未记录");

        // 如果有异常，记录异常信息
        if (ex != null) {
            detailInfo.put("exception", ex.getClass().getName());
            detailInfo.put("exceptionMessage", ex.getMessage());
            LoggerUtil.detailError(logger, "响应详情(异常): {}", ex, JSON.toJSONString(detailInfo));
        } else {
            LoggerUtil.detailInfo(logger, "响应详情: {}", JSON.toJSONString(detailInfo));
        }

        // 清理线程本地变量
        cleanup();
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    /**
     * 清理线程本地变量
     */
    private void cleanup() {
        startTimeHolder.remove();
        traceIdHolder.remove();
    }
} 