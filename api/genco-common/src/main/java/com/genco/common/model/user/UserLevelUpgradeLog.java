package com.genco.common.model.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户等级升级日志表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("eb_user_level_upgrade_log")
@ApiModel(value = "UserLevelUpgradeLog对象", description = "用户等级升级日志表")
public class UserLevelUpgradeLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "用户ID")
    private Integer uid;

    @ApiModelProperty(value = "原等级ID")
    private Integer fromLevelId;

    @ApiModelProperty(value = "目标等级ID")
    private Integer toLevelId;

    @ApiModelProperty(value = "升级方式：1-付费购买，2-线下申请，3-渠道合作，4-系统迁移")
    private Integer upgradeType;

    @ApiModelProperty(value = "关联订单号")
    private String orderNo;

    @ApiModelProperty(value = "操作人ID（管理员手动升级时使用）")
    private Integer operatorId;

    @ApiModelProperty(value = "升级备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    // 升级方式常量
    public static final int TYPE_PURCHASE = 1;    // 付费购买
    public static final int TYPE_APPLY = 2;       // 线下申请
    public static final int TYPE_COOPERATION = 3; // 渠道合作
    public static final int TYPE_MIGRATION = 4;   // 系统迁移
}
