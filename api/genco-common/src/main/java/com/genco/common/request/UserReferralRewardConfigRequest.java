package com.genco.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 用户拉新奖励配置请求对象
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "UserReferralRewardConfigRequest对象", description = "用户拉新奖励配置请求")
public class UserReferralRewardConfigRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "拉新数量要求")
    @NotNull(message = "拉新数量不能为空")
    @Min(value = 1, message = "拉新数量必须大于0")
    private Integer referralCount;

    @ApiModelProperty(value = "首单数量要求")
    @NotNull(message = "首单数量不能为空")
    @Min(value = 0, message = "首单数量不能小于0")
    private Integer firstOrderCount;

    @ApiModelProperty(value = "奖励金额（印尼盾）")
    @NotNull(message = "奖励金额不能为空")
    @DecimalMin(value = "0", message = "奖励金额不能小于0")
    private BigDecimal rewardAmount;

    @ApiModelProperty(value = "状态：0-禁用，1-启用")
    private Integer status = 1;
}
