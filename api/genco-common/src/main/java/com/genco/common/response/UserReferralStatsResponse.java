package com.genco.common.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 用户拉新统计数据响应类
 */
@Data
@ApiModel(value = "UserReferralStatsResponse对象", description = "用户拉新统计数据响应")
public class UserReferralStatsResponse {

    @ApiModelProperty(value = "用户余额（印尼盾）")
    private BigDecimal userBalance;

    @ApiModelProperty(value = "累计邀请人数")
    private Integer totalReferralCount;

    @ApiModelProperty(value = "已兑换邀请人数")
    private Integer redeemedReferralCount;

    @ApiModelProperty(value = "累计首单数")
    private Integer totalFirstOrderCount;

    @ApiModelProperty(value = "已兑换首单数")
    private Integer redeemedFirstOrderCount;
}
