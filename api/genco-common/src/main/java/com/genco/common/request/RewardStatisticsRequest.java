package com.genco.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * 奖励统计查询请求对象
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "RewardStatisticsRequest对象", description = "奖励统计查询请求")
public class RewardStatisticsRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "开始日期，格式：yyyy-MM-dd")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "开始日期格式错误，应为yyyy-MM-dd")
    private String startDate;

    @ApiModelProperty(value = "结束日期，格式：yyyy-MM-dd")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "结束日期格式错误，应为yyyy-MM-dd")
    private String endDate;

    @ApiModelProperty(value = "奖励类型，如：invite_first_order_reward")
    private String rewardType;

    @ApiModelProperty(value = "用户ID，查询特定用户的奖励记录")
    private Integer uid;

    @ApiModelProperty(value = "用户昵称，模糊查询")
    private String nickname;

    @ApiModelProperty(value = "页码，默认1")
    private Integer page = 1;

    @ApiModelProperty(value = "每页大小，默认20")
    private Integer limit = 20;
}
