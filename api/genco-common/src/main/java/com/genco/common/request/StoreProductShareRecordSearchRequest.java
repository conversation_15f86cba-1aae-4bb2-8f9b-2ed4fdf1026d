package com.genco.common.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class StoreProductShareRecordSearchRequest {
    @ApiModelProperty(value = "关键词搜索，支持用户昵称、商品名称、商品ID等")
    private String keyword;

    @ApiModelProperty(value = "品牌代码，用于筛选特定品牌的商品")
    private String brandCode;

    private Long userId;
    private String tiktokUid;
    private String userAccount;
    private String productId;
    private String productName;
    private String channel;
    private String operateTimeStart;
    private String operateTimeEnd;
}