# Genco Project Makefile
# This Makefile provides common development tasks for the Genco project

# Include common variables and functions
include scripts/make-rules/common.mk
include scripts/make-rules/build.mk
include scripts/make-rules/test.mk
include scripts/make-rules/deploy.mk
include scripts/make-rules/service.mk

.DEFAULT_GOAL := help

# Help target
.PHONY: help
help: ## Show this help message
	@echo "$(BLUE)Genco Project Makefile$(RESET)"
	@echo "Available targets:"
	@echo ""
	@echo "$(YELLOW)Build targets:$(RESET)"
	@echo "  build              Build all components (API + Admin UI)"
	@echo "  build-api          Build API (Spring Boot)"
	@echo "  build-admin-ui     Build Admin UI (Vue.js)"
	@echo "  build-app-ui       Build App UI (Flutter)"
	@echo "  compile            Compile API without packaging"
	@echo "  clean              Clean build artifacts"
	@echo "  install-deps       Install all dependencies"
	@echo ""
	@echo "$(YELLOW)Test targets:$(RESET)"
	@echo "  test               Run all tests"
	@echo "  test-api           Run API tests"
	@echo "  test-admin-ui      Run Admin UI tests"
	@echo "  test-integration   Run integration tests"
	@echo "  test-referral-reward Test referral reward endpoints"
	@echo "  test-performance   Run performance tests"
	@echo "  login-test         Simulate user login and save token"
	@echo ""
	@echo "$(YELLOW)Deployment targets:$(RESET)"
	@echo "  deploy-dev         Deploy to development environment"
	@echo "  deploy-prod        Deploy to production environment"
	@echo "  docker-build       Build Docker images"
	@echo "  docker-push        Push Docker images to registry"
	@echo "  deploy-docker      Deploy using Docker"
	@echo ""
	@echo "$(YELLOW)Service management (File-based Commands):$(RESET)"
	@echo "  $(GREEN)Service Module (service.mk):$(RESET)"
	@echo "    service.admin.start      Start genco-admin (Backend Admin API)"
	@echo "    service.admin.stop       Stop genco-admin service"
	@echo "    service.admin.restart    Restart genco-admin service"
	@echo "    service.front.start      Start genco-front (Frontend User API)"
	@echo "    service.front.stop       Stop genco-front service"
	@echo "    service.front.restart    Restart genco-front service"
	@echo "    service.admin-ui.start   Start adminUI (Management Frontend)"
	@echo "    service.admin-ui.stop    Stop adminUI service"
	@echo "    service.admin-ui.restart Restart adminUI service"
	@echo "    service.all.start        Start all three components"
	@echo "    service.all.stop         Stop all services"
	@echo "    service.all.restart      Restart all services"
	@echo ""
	@echo "  $(GREEN)Common Module (common.mk):$(RESET)"
	@echo "    common.logs.admin        Show genco-admin logs"
	@echo "    common.logs.front        Show genco-front logs"
	@echo "    common.logs.admin-ui     Show adminUI logs"
	@echo "    common.port.admin        Get genco-admin port"
	@echo "    common.port.front        Get genco-front port"
	@echo "    common.port.admin-ui     Get adminUI port"
	@echo "    common.status.all        Show status of all services"

	@echo ""
	@echo "$(YELLOW)Utility Commands:$(RESET)"
	@echo "  show-ports         Show detected port configurations"
	@echo "  health-check       Perform health check"
	@echo ""
	@echo "$(YELLOW)Development targets:$(RESET)"
	@echo "  dev                Start development environment"
	@echo "  logs               Show service logs"
	@echo "  db-migrate         Run database migrations"
	@echo "  lint               Run code linting"
	@echo "  format             Format code"
	@echo ""
	@echo "$(YELLOW)Utility targets:$(RESET)"
	@echo "  check-env          Check development environment"
	@echo "  version            Show project version"
	@echo "  demo               Show startup capabilities demo"
	@echo "  test-paths         Test dynamically generated path variables"
	@echo "  help               Show this help message"

# Development environment
.PHONY: dev
dev: check-env install-deps ## Start development environment
	$(call log_info,"Starting development environment...")
	@echo "Starting API in development mode..."
	cd $(API_DIR) && mvn spring-boot:run -Dspring-boot.run.profiles=dev &
	@echo "Starting Admin UI in development mode..."
	cd $(ADMIN_UI_DIR) && npm run serve &
	$(call log_success,"Development environment started")
	@echo "API: http://localhost:8080"
	@echo "Admin UI: http://localhost:8081"
	@echo "API Documentation: http://localhost:8080/doc.html"

# Show Docker logs (legacy command)
.PHONY: logs-docker
logs-docker: ## Show Docker service logs
	$(call log_info,"Showing Docker service logs...")
	docker-compose -f $(API_DIR)/docker-compose.yaml logs -f

# Show all service logs
.PHONY: logs
logs: ## Show all service logs
	$(call log_info,"Showing all service logs...")
	@echo "$(BLUE)Available log commands:$(RESET)"
	@echo "  make logs-admin     - Show genco-admin logs"
	@echo "  make logs-front     - Show genco-front logs"
	@echo "  make logs-admin-ui  - Show adminUI logs"
	@echo "  make logs-docker    - Show Docker service logs"

# Database migration
.PHONY: db-migrate
db-migrate: ## Run database migrations
	$(call log_info,"Running database migrations...")
	cd $(API_DIR) && mvn flyway:migrate
	$(call log_success,"Database migrations completed")

# Code linting
.PHONY: lint
lint: ## Run code linting
	$(call log_info,"Running code linting...")
	cd $(API_DIR) && mvn checkstyle:check
	cd $(ADMIN_UI_DIR) && npm run lint
	$(call log_success,"Code linting completed")

# Code formatting
.PHONY: format
format: ## Format code
	$(call log_info,"Formatting code...")
	cd $(API_DIR) && mvn spotless:apply
	cd $(ADMIN_UI_DIR) && npm run lint:fix
	$(call log_success,"Code formatting completed")

# Check development environment
.PHONY: check-env
check-env: ## Check development environment
	$(call log_info,"Checking development environment...")
	$(call check_command,java)
	$(call check_command,mvn)
	$(call check_command,node)
	$(call check_command,npm)
	@java -version
	@mvn -version
	@node --version
	@npm --version
	$(call log_success,"Development environment check completed")

# Show project version
.PHONY: version
version: ## Show project version
	@echo "$(BLUE)Genco Project Version Information$(RESET)"
	@echo "Project: $(PROJECT_NAME)"
	@cd $(API_DIR) && mvn help:evaluate -Dexpression=project.version -q -DforceStdout
	@echo "Build Date: $(shell date)"
	@echo "Git Commit: $(shell git rev-parse --short HEAD 2>/dev/null || echo 'unknown')"

# Show startup capabilities demo
.PHONY: demo
demo: ## Show startup capabilities demo
	@./scripts/demo-startup.sh

# Test path variables
.PHONY: test-paths
test-paths: ## Test dynamically generated path variables
	@echo "$(BLUE)Testing dynamically generated path variables:$(RESET)"
	@echo "PROJECT_NAME: $(PROJECT_NAME)"
	@echo "API_DIR: $(API_DIR)"
	@echo "ADMIN_UI_DIR: $(ADMIN_UI_DIR)"
	@echo "APP_UI_DIR: $(APP_UI_DIR)"
	@echo ""
	@echo "$(BLUE)Verifying directories exist:$(RESET)"
	@if [ -d "$(API_DIR)" ]; then echo "✅ API_DIR exists: $(API_DIR)"; else echo "❌ API_DIR missing: $(API_DIR)"; fi
	@if [ -d "$(ADMIN_UI_DIR)" ]; then echo "✅ ADMIN_UI_DIR exists: $(ADMIN_UI_DIR)"; else echo "❌ ADMIN_UI_DIR missing: $(ADMIN_UI_DIR)"; fi
	@if [ -d "$(APP_UI_DIR)" ]; then echo "✅ APP_UI_DIR exists: $(APP_UI_DIR)"; else echo "❌ APP_UI_DIR missing: $(APP_UI_DIR)"; fi

# Quick setup for new developers
.PHONY: setup
setup: check-env install-deps ## Quick setup for new developers
	$(call log_info,"Setting up development environment...")
	@echo "Creating necessary directories..."
	@mkdir -p logs
	@mkdir -p temp
	$(call log_success,"Setup completed! Run 'make dev' to start development")

# Clean everything
.PHONY: clean-all
clean-all: clean ## Clean everything including dependencies
	$(call log_info,"Cleaning everything...")
	cd $(ADMIN_UI_DIR) && rm -rf node_modules
	cd $(APP_UI_DIR) && rm -rf .dart_tool
	$(call log_success,"Everything cleaned")
