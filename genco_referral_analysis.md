# Genco项目用户推荐关系绑定机制分析

## 概述
本文档详细分析了genco项目中用户推荐关系的绑定机制，包括数据库设计、业务逻辑、代码实现和推荐层级关系。

## 快速检索关键词
- 推荐关系绑定
- 邀请码机制
- 分销系统
- 多级推荐
- UserServiceImpl
- spread_uid字段
- 返佣比例配置

## 1. 数据库层面分析

### 核心推荐关系字段（eb_user表）

| 字段名 | 类型 | 说明 |
|--------|------|------|
| spread_uid | Integer | 推广人ID，建立父子关系 |
| spread_time | Date | 推广员关联时间 |
| spread_count | Integer | 下级人数 |
| path | String | 用户推广等级路径 |
| invite_code | String | 邀请码 |

### 实际推荐关系数据
- uid=21 被 uid=3 推荐（spread_uid=3）
- uid=3 被 uid=6 推荐（spread_uid=6）
- 形成推荐链条：uid=6 → uid=3 → uid=21

### 推荐关系存储逻辑
- **spread_uid**: 直接推荐人的用户ID
- **spread_count**: 直接推荐的下级用户数量
- **spread_time**: 推荐关系建立时间戳
- **path**: 推荐层级路径（当前统一为"/0/"）
- **invite_code**: 唯一邀请码，6位字符（5个字母+1个数字）

## 2. 业务逻辑层面分析

### 推荐关系绑定验证规则
1. 推荐人ID不能为空或0
2. 用户不能已有推荐关系（spread_uid > 0）
3. 不能推荐自己
4. 分销功能必须启用
5. 根据分销模式验证推荐人资格

### 推荐码生成机制
- 生成5个大写字母 + 1个数字
- 随机打乱顺序形成6位邀请码
- 确保唯一性（数据库查重）

### 推荐关系绑定触发时机
1. **用户注册时**：通过spreadUid参数绑定
2. **登录用户主动绑定**：通过邀请码绑定

### 业务规则配置
- brokerage_func_status = 1（分销功能启用）
- store_brokerage_status = 1（指定分销模式）
- brokerage_bindind = 0（适用所有用户）
- store_brokerage_ratio = 10（一级返佣10%）
- store_brokerage_two = 5（二级返佣5%）

## 3. 代码实现分析

### 核心Service类：UserServiceImpl

#### 主要方法
- `checkBingSpread()`: 验证推荐关系绑定合法性
- `bindSpread()`: 执行推荐关系绑定
- `updateSpreadCountByUid()`: 更新推荐人下级数量
- `bindInviteCode()`: 邀请码绑定
- `clearSpread()`: 清除推荐关系

#### 关键代码片段
```java
// 推荐关系验证
public Boolean checkBingSpread(User user, Integer spreadUid, String type) {
    // 基础验证
    if (ObjectUtil.isNull(spreadUid) || spreadUid <= 0 || user.getSpreadUid() > 0) {
        return false;
    }
    // 分销功能开启检查
    String isOpen = systemConfigService.getValueByKey(Constants.CONFIG_KEY_STORE_BROKERAGE_IS_OPEN);
    if (StrUtil.isBlank(isOpen) || isOpen.equals("0")) {
        return false;
    }
    // 其他业务规则验证...
}

// 推荐关系绑定
private Boolean bindSpread(User user, Integer spreadUid) {
    Boolean checkBingSpread = checkBingSpread(user, spreadUid, "old");
    if (!checkBingSpread) return false;
    
    user.setSpreadUid(spreadUid);
    user.setSpreadTime(DateUtil.nowDateTime());
    
    // 事务处理
    Boolean execute = transactionTemplate.execute(e -> {
        updateById(user);
        updateSpreadCountByUid(spreadUid, "add");
        return Boolean.TRUE;
    });
    return execute;
}
```

### Controller接口
- `/api/front/user/bindSpread`: 绑定推广关系（GET）
- `/api/front/user/bindInviteCode`: 绑定邀请码（POST）

### 配置常量
```java
public static final String CONFIG_KEY_STORE_BROKERAGE_IS_OPEN = "brokerage_func_status";
public static final String CONFIG_KEY_STORE_BROKERAGE_MODEL = "store_brokerage_status";
public static final String CONFIG_KEY_STORE_BROKERAGE_RATE_ONE = "store_brokerage_ratio";
public static final String CONFIG_KEY_STORE_BROKERAGE_RATE_TWO = "store_brokerage_two";
```

## 4. 推荐层级关系

### 多级推荐支持
- 系统支持最多3级推荐关系
- 通过递归查询spread_uid建立推荐链条
- 用于奖励计算和分佣处理

### 推荐链路获取
```java
// 获取3级推荐链路
User up1 = user.getSpreadUid() != null && user.getSpreadUid() > 0 ? 
    userService.getInfoByUid(user.getSpreadUid()) : null;
User up2 = (up1 != null && up1.getSpreadUid() != null && up1.getSpreadUid() > 0) ? 
    userService.getInfoByUid(up1.getSpreadUid()) : null;
User up3 = (up2 != null && up2.getSpreadUid() != null && up2.getSpreadUid() > 0) ? 
    userService.getInfoByUid(up2.getSpreadUid()) : null;
```

### path字段说明
- 设计用于存储推荐层级路径
- 当前实现中统一为"/0/"
- 清除推荐关系时重置为"/0/"
- 为未来扩展复杂层级功能预留

## 5. 推荐关系变更处理

### 事务安全
- 所有推荐关系变更使用事务处理
- 确保用户信息和推荐人统计数据一致性

### 推荐关系清除
```java
public boolean clearSpread(Integer userId) {
    User user = new User();
    user.setUid(userId);
    user.setPath("/0/");
    user.setSpreadUid(0);
    user.setSpreadTime(null);
    
    Boolean execute = transactionTemplate.execute(e -> {
        userDao.updateById(user);
        if (teamUser.getSpreadUid() > 0) {
            updateSpreadCountByUid(teamUser.getSpreadUid(), "sub");
        }
        return Boolean.TRUE;
    });
    return execute;
}
```

## 6. 系统特点总结

1. **简洁的数据结构**：通过spread_uid建立直接父子关系
2. **完善的验证机制**：防止循环推荐、重复绑定等异常
3. **灵活的绑定方式**：支持注册时绑定和邀请码绑定
4. **事务安全**：确保数据一致性
5. **配置化管理**：通过系统配置控制分销参数
6. **扩展性设计**：path字段为复杂层级功能预留空间

## 7. 相关文件路径

### 核心文件
- `api/genco-common/src/main/java/com/genco/common/model/user/User.java`
- `api/genco-service/src/main/java/com/genco/service/service/impl/UserServiceImpl.java`
- `api/genco-front/src/main/java/com/genco/front/controller/UserController.java`
- `api/genco-common/src/main/java/com/genco/common/utils/CommonUtil.java`
- `api/genco-common/src/main/java/com/genco/common/constants/Constants.java`

### 数据库配置
- 主机: 35.198.200.73:53306
- 数据库: genco_data
- 主要表: eb_user, eb_system_config

---
*分析时间: 2025-08-02*
*项目版本: genco-front*
