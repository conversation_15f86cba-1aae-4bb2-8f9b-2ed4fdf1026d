// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import i18n from '@/i18n' 
import * as constants from '@/utils/constants.js'
import { formatDates } from '@/utils/index';

// 公共过滤器
export function filterEmpty(val) {
  let _result = '-'
  if (!val) {
    return _result
  }
  _result = val
  return _result
}

// 时间过滤器
export function formatDate(time) {
  if (time !== 0) {
    const date = new Date(time * 1000);
    return formatDates(date, 'yyyy-MM-dd hh:mm');
  }
}

export function filterYesOrNo(value) {
  return value ? i18n.t('common.yes') : i18n.t('common.no');
}

export function filterShowOrHide(value) {
  return value ? i18n.t('common.show') : i18n.t('common.hide');
}

export function filterShowOrHideForFormConfig(value) {
  return value === '‘0’' ? i18n.t('common.show') : i18n.t('common.hide')
}

export function filterYesOrNoIs(value) {
  return value ? i18n.t('common.no') : i18n.t('common.yes');
}

export function filterCategroyType(value) {
  return constants.categoryType.filter(item => value === item.value)[0].name
}

export function filterConfigCategory(value) {
  return constants.configCategory.filter(item => value === item.value)[0].label
}

/**
 * @description 公众号回复类型
 */
export function keywordStatusFilter(status) {
const statusMap = {
  'text': i18n.t('common.keyword.text'),
  'image': i18n.t('common.keyword.image'),
  'news': i18n.t('common.keyword.news'),
  'voice': i18n.t('common.keyword.voice')
}
  return statusMap[status]
}

/**
 * @description 优惠券类型
 */
export function couponUserTypeFilter(status) {
 const statusMap = {
  1: i18n.t('common.couponType.general'),
  2: i18n.t('common.couponType.product'),
  3: i18n.t('common.couponType.category')
}
  return statusMap[status]
}

/**
 * @description 优惠券领取方式
 */
export function couponTypeFilter(status) {
const statusMap = {
  1: i18n.t('common.couponReceive.manual'),
  2: i18n.t('common.couponReceive.newUser'),
  3: i18n.t('common.couponReceive.gift')
}
  return statusMap[status]
}

/**
 * @description 文章分类
 */
export function articleTypeFilter(status) {
  if(!status){
    return ''
  }
  let arrayList = JSON.parse(localStorage.getItem('articleClass'));
  if(arrayList.filter(item => Number(status) === Number(item.id)).length < 1){
    return ''
  }
  return arrayList.filter(item => Number(status) === Number(item.id))[0].name
}


/**
 * @description 支付状态
 */
export function payStatusFilter(status) {
const statusMap = {
  false: i18n.t('common.paymentStatus.unpaid'),
  true: i18n.t('common.paymentStatus.paid')
}
  return statusMap[status]
}

/**
 * @description 提现方式
 */
export function extractTypeFilter(status) {
const statusMap = {
  'bank': i18n.t('common.withdrawType.bank'),
  'alipay': i18n.t('common.withdrawType.alipay'),
  'weixin': i18n.t('common.withdrawType.wechat')
}
  return statusMap[status]
}

/**
 * @description 充值类型
 */
export function rechargeTypeFilter(status) {
const statusMap = {
  'public': i18n.t('common.rechargeType.wechatPublic'),
  'weixinh5': i18n.t('common.rechargeType.wechatH5'),
  'routine': i18n.t('common.rechargeType.miniProgram')
}
  return statusMap[status]
}

/**
 * @description 财务审核状态
 */
export function extractStatusFilter(status) {
 const statusMap = {
  '-1': i18n.t('common.withdrawStatus.rejected'),
  '0': i18n.t('common.withdrawStatus.reviewing'),
  '1': i18n.t('common.withdrawStatus.withdrawn')
}
  return statusMap[status]
}

/**
 * @description 砍价状态
 */
export function bargainStatusFilter(status) {
const statusMap = {
  '1': i18n.t('common.status.bargain.1'),
  '2': i18n.t('common.status.bargain.2'),
  '3': i18n.t('common.status.bargain.3')
}
  return statusMap[status]
}

/**
 * @description 砍价状态
 */
export function bargainColorFilter(status) {
  const statusMap = {
    '1': '',
    '2': 'danger',
    '3': 'success'
  }
  return statusMap[status]
}

/**
 * @description 拼团状态
 */
export function groupStatusFilter(status) {
const statusMap = {
  '1': i18n.t('common.status.bargain.1'),
  '2': i18n.t('common.status.bargain.2'),
  '3': i18n.t('common.status.bargain.3')
}
  return statusMap[status]
}

/**
 * @description 拼团状态
 */
export function groupColorFilter(status) {
  const statusMap = {
    '1': '',
    '2': 'success',
    '3': 'danger'
  }
  return statusMap[status]
}

/**
 * @description 一号通tab值
 */
export function onePassTypeFilter(status) {
 const statusMap = {
  'sms': i18n.t('common.onePass.sms'),
  'copy': i18n.t('common.onePass.copy'),
  'expr_query': i18n.t('common.onePass.expr_query'),
  'expr_dump': i18n.t('common.onePass.expr_dump')
}
  return statusMap[status]
}

/**
 * @description 视频号商品草稿状态
 */
export function editStatusFilter(status) {
const statusMap = {
  1: i18n.t('common.editStatus.1'),
  2: i18n.t('common.editStatus.2'),
  3: i18n.t('common.editStatus.3'),
  4: i18n.t('common.editStatus.4')
}
  return statusMap[status]
}

/**
 * @description 视频号正式商品状态
 */
export function videoStatusFilter(status) {
 const statusMap = {
  0: i18n.t('common.videoStatus.0'),
  5: i18n.t('common.videoStatus.5'),
  11: i18n.t('common.videoStatus.11'),
  13: i18n.t('common.videoStatus.13')
}
  return statusMap[status]
}
