import request from '@/utils/request'

/**
 * TikTok Shop API相关接口
 */

/**
 * 联盟选品查询（V202405版本）
 * @param {Object} params 查询参数 - 与AffiliateProductSearchRequest保持一致
 * @param {Number} params.pageSize 每页数量，默认20，最大100
 * @param {String} params.cursor 分页游标，用于获取下一页数据
 * @param {String} params.sortField 排序字段：commission_rate-佣金率，price-价格，sales-销量，created_time-创建时间
 * @param {String} params.sortOrder 排序方向：ASC-升序，DESC-降序
 * @param {Array} params.titleKeywords 商品标题关键词列表，用于模糊搜索
 * @param {Number} params.salesPriceMin 最低销售价格
 * @param {Number} params.salesPriceMax 最高销售价格
 * @param {Number} params.commissionRateMin 最低佣金率（百分比）
 * @param {Number} params.commissionRateMax 最高佣金率（百分比）
 */
export function searchAffiliateProducts(params) {
  return request({
    url: '/admin/affiliate/products/search',
    method: 'post',
    data: params
  })
}
