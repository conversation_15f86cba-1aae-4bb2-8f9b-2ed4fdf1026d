<template>
  <div class="app-container">
    <!-- 统计概览卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="card-content">
            <div class="card-title">总奖励金额</div>
            <div class="card-value">Rp {{ formatIDR(statistics.totalRewardAmount || 0) }}</div>
            <div class="card-desc">累计发放次数：{{ statistics.totalRewardCount || 0 }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="card-content">
            <div class="card-title">今日奖励</div>
            <div class="card-value">Rp {{ formatIDR(statistics.todayRewardAmount || 0) }}</div>
            <div class="card-desc">今日发放次数：{{ statistics.todayRewardCount || 0 }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="card-content">
            <div class="card-title">本月奖励</div>
            <div class="card-value">Rp {{ formatIDR(statistics.monthRewardAmount || 0) }}</div>
            <div class="card-desc">本月发放次数：{{ statistics.monthRewardCount || 0 }}</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="statistics-card">
          <div class="card-content">
            <div class="card-title">平均奖励</div>
            <div class="card-value">Rp {{ formatIDR(averageReward) }}</div>
            <div class="card-desc">单次平均金额</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 查询条件 -->
    <el-card class="mb20">
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="时间范围：">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            @change="onDateRangeChange">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="奖励类型：">
          <el-select v-model="searchForm.rewardType" placeholder="请选择奖励类型" clearable>
            <el-option label="邀请首单奖励" value="invite_first_order_reward"></el-option>
            <el-option label="代理邀请奖励" value="agent_invite_reward"></el-option>
            <el-option label="其他奖励" value="other_reward"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="用户昵称：">
          <el-input v-model="searchForm.nickname" placeholder="请输入用户昵称" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchList">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button type="success" @click="exportData">导出</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 奖励明细表格 -->
    <el-card>
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        :header-cell-style="{fontWeight:'bold'}">
        <el-table-column prop="uid" label="用户ID" width="80"></el-table-column>
        <el-table-column prop="nickname" label="用户昵称" width="120"></el-table-column>
        <el-table-column prop="rewardTitle" label="奖励标题" width="150"></el-table-column>
        <el-table-column prop="rewardType" label="奖励类型" width="150">
          <template slot-scope="scope">
            <el-tag :type="getRewardTypeColor(scope.row.rewardType)">
              {{ getRewardTypeName(scope.row.rewardType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="rewardAmount" label="奖励金额" width="120">
          <template slot-scope="scope">
            <span class="reward-amount">Rp {{ formatIDR(scope.row.rewardAmount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="发放时间" width="160"></el-table-column>
        <el-table-column prop="mark" label="备注" min-width="200"></el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="searchForm.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="searchForm.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </el-card>
  </div>
</template>

<script>
import { getRewardStatistics, getRewardDetails, exportRewardDetails } from '@/api/reward-statistics'

export default {
  name: 'RewardStatistics',
  data() {
    return {
      loading: false,
      statistics: {},
      tableData: [],
      total: 0,
      dateRange: [],
      searchForm: {
        startDate: '',
        endDate: '',
        rewardType: '',
        nickname: '',
        page: 1,
        limit: 20
      }
    }
  },
  computed: {
    averageReward() {
      if (this.statistics.totalRewardCount > 0) {
        return (this.statistics.totalRewardAmount / this.statistics.totalRewardCount).toFixed(0)
      }
      return 0
    }
  },
  created() {
    this.loadStatistics()
    this.loadDetails()
  },
  methods: {
    // 加载统计数据
    async loadStatistics() {
      try {
        const res = await getRewardStatistics(this.searchForm)
        this.statistics = res.data || {}
      } catch (error) {
        this.$message.error('加载统计数据失败')
      }
    },
    // 加载明细数据
    async loadDetails() {
      this.loading = true
      try {
        const res = await getRewardDetails(this.searchForm)
        this.tableData = res.data.list || []
        this.total = res.data.total || 0
      } catch (error) {
        this.$message.error('加载明细数据失败')
      } finally {
        this.loading = false
      }
    },
    // 日期范围变化
    onDateRangeChange(dates) {
      if (dates && dates.length === 2) {
        this.searchForm.startDate = dates[0]
        this.searchForm.endDate = dates[1]
      } else {
        this.searchForm.startDate = ''
        this.searchForm.endDate = ''
      }
    },
    // 查询
    searchList() {
      this.searchForm.page = 1
      this.loadStatistics()
      this.loadDetails()
    },
    // 重置
    resetSearch() {
      this.dateRange = []
      this.searchForm = {
        startDate: '',
        endDate: '',
        rewardType: '',
        nickname: '',
        page: 1,
        limit: 20
      }
      this.loadStatistics()
      this.loadDetails()
    },
    // 导出
    async exportData() {
      try {
        this.$message.info('正在导出，请稍候...')
        const res = await exportRewardDetails(this.searchForm)
        if (res.data) {
          this.$message.success('导出成功')
          // 这里可以添加下载逻辑
        }
      } catch (error) {
        this.$message.error('导出失败')
      }
    },
    // 分页
    handleSizeChange(val) {
      this.searchForm.limit = val
      this.loadDetails()
    },
    handleCurrentChange(val) {
      this.searchForm.page = val
      this.loadDetails()
    },
    // 格式化印尼盾
    formatIDR(amount) {
      if (!amount) return '0'
      return Number(amount).toLocaleString('id-ID')
    },
    // 获取奖励类型名称
    getRewardTypeName(type) {
      const typeMap = {
        'invite_first_order_reward': '邀请首单奖励',
        'agent_invite_reward': '代理邀请奖励',
        'other_reward': '其他奖励'
      }
      return typeMap[type] || type
    },
    // 获取奖励类型颜色
    getRewardTypeColor(type) {
      const colorMap = {
        'invite_first_order_reward': 'success',
        'agent_invite_reward': 'warning',
        'other_reward': 'info'
      }
      return colorMap[type] || 'info'
    }
  }
}
</script>

<style scoped>
.statistics-card {
  text-align: center;
}
.card-content {
  padding: 10px;
}
.card-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}
.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 5px;
}
.card-desc {
  font-size: 12px;
  color: #999;
}
.reward-amount {
  font-weight: bold;
  color: #67C23A;
}
.mb20 {
  margin-bottom: 20px;
}
</style>
