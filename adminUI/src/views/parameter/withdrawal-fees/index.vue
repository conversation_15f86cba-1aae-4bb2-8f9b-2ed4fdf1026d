<template>
  <div class="divBox">
    <el-card class="box-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        size="small"
        :header-cell-style="{ fontWeight: 'bold' }"
      >
        <el-table-column type="index" :label="$t('common.serialNumber')"  width="110">
        </el-table-column>
        <el-table-column
          :label="$t('parameter.withdrawalFee.feeTemplateId')"
          min-width="80"
          prop="id"
        >
        </el-table-column>
        <el-table-column
          :label="$t('parameter.withdrawalFee.minWithdrawAmount')"
          min-width="150"
          prop="min_withdraw_amount"
        >
        </el-table-column>
        <el-table-column
          :label="$t('parameter.withdrawalFee.maxWithdrawAmount')"
          min-width="150"
          prop="max_withdraw_amount"
        >
        </el-table-column>
        <el-table-column
          :label="$t('parameter.withdrawalFee.withdrawFeeRate')"
          min-width="150"
          prop="withdraw_fee_rate"
        >
        </el-table-column>
        <el-table-column
          fixed="right"
          :label="$t('parameter.withdrawalFee.operation')"
          min-width="80"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="handleEdit(scope.row)"
              >{{ $t("parameter.withdrawalFee.edit") }}</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-dialog
        append-to-body
        :visible.sync="dialogFormVisible"
        :title="dialogTitle"
        width="680px"
        @close="handleCancle"
      >
        <el-form
          ref="elForm"
          inline
          :model="form"
          :rules="rules"
          label-width="200px"
        >
          <el-form-item
            :label="$t('parameter.withdrawalFee.feeTemplateId') + '：'"
          >
            <el-input
              v-model="form.id"
              size="small"
              :placeholder="$t('parameter.withdrawalFee.placeholder.couponId')"
              disabled
            ></el-input>
          </el-form-item>
          <el-form-item
            :label="$t('parameter.withdrawalFee.minWithdrawAmount') + '：'"
            prop="min_withdraw_amount"
          >
            <el-input
              v-model="form.min_withdraw_amount"
              size="small"
              :placeholder="
                $t('parameter.withdrawalFee.placeholder.minWithdrawAmount')
              "
            ></el-input>
          </el-form-item>
          <el-form-item
            :label="$t('parameter.withdrawalFee.maxWithdrawAmount') + '：'"
            prop="max_withdraw_amount"
          >
            <el-input
              v-model="form.max_withdraw_amount"
              size="small"
              :placeholder="
                $t('parameter.withdrawalFee.placeholder.maxWithdrawAmount')
              "
            ></el-input>
          </el-form-item>
          <el-form-item
            :label="$t('parameter.withdrawalFee.withdrawFeeRate') + '：'"
            prop="withdraw_fee_rate"
          >
            <el-input
              v-model="form.withdraw_fee_rate"
              size="small"
              placeholder="0"
            ></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer">
          <el-button type="primary" @click="handelConfirm">{{
            $t("common.confirm")
          }}</el-button>
          <el-button @click="handleCancle">{{ $t("common.cancel") }}</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import { configInfo, configSaveForm } from "@/api/parameter";
export default {
  name: "WithdrawalFees",
  data() {
    return {
      loading: false,
      searchFrom: {
        formId: 103
      },
      tableData: [],
      dialogTitle: this.$t("parameter.withdrawalFee.addTitle"),
      dialogFormVisible: false,
      form: {
        couponId: "",
        min_withdraw_amount: "",
        max_withdraw_amount: "",
        withdraw_fee_rate: 0
      },
      rules: {
        min_withdraw_amount: [
          {
            required: true,
            message: this.$t(
              "parameter.withdrawalFee.placeholder.minWithdrawAmount"
            ),
            trigger: "blur"
          }
        ],
        max_withdraw_amount: [
          {
            required: true,
            message: this.$t(
              "parameter.withdrawalFee.placeholder.maxWithdrawAmount"
            ),
            trigger: "blur"
          }
        ],
        withdraw_fee_rate: [
          {
            required: true,
            message: this.$t(
              "parameter.withdrawalFee.placeholder.withdrawFeeRate"
            ),
            trigger: "blur"
          }
        ]
      }
    };
  },
  created() {},
  mounted() {
    this.getList();
  },
  methods: {
    // 列表
    getList(num) {
      this.loading = true;

      configInfo(this.searchFrom)
        .then(res => {
          if (res) {
            this.tableData = [res];
          }
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    handleEdit(row) {
      this.dialogTitle = this.$t("parameter.withdrawalFee.editTitle");
      this.dialogFormVisible = true;
      this.form.id = row.id;
      this.form.min_withdraw_amount = row.min_withdraw_amount;
      this.form.max_withdraw_amount = row.max_withdraw_amount;
      this.form.withdraw_fee_rate = row.withdraw_fee_rate;
    },
    handleCancle() {
      (this.form = {
        id: "",
        min_withdraw_amount: "",
        max_withdraw_amount: "",
        withdraw_fee_rate: 0
      }),
        (this.dialogFormVisible = false);
    },
    handelConfirm() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return;
        const param = {
          id: this.form.id,
          sort: 1,
          status: true,
          fields: [
            {
              name: "min_withdraw_amount",
              value: this.form.min_withdraw_amount,
              title: "min_withdraw_amount"
            },
            {
              name: "max_withdraw_amount",
              value: this.form.max_withdraw_amount,
              title: "max_withdraw_amount"
            },
            {
              name: "withdraw_fee_rate",
              value: this.form.withdraw_fee_rate,
              title: "withdraw_fee_rate"
            }
          ]
        };
        configSaveForm(param).then(res => {
          this.$message.success(this.$t("common.operationSuccess"));

          this.handleCancle();
          this.getList();
        });
      });
    }
  }
};
</script>

<style scoped lang="scss"></style>
