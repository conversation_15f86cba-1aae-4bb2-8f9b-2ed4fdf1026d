import Layout from '@/layout'

const appRouter = {
  path: '/app',
  component: Layout,
  redirect: '/app/index',
  name: 'Product',
  meta: {
    title: 'appManage',
    icon: 'clipboard'
  },
  children: [
    {
      path: 'home',
      component: () => import('@/views/app/home'),
      name: 'home',
      meta: { title: 'homeManage', icon: '' }
    },
    // {
    //   path: 'grade',
    //   component: () => import('@/views/user/grade/index'),
    //   name: 'Grade',
    //   meta: { title: '用户等级', icon: '' }
    // },
    // {
    //   path: 'label',
    //   component: () => import('@/views/user/group/index'),
    //   name: 'Label',
    //   meta: { title: '用户标签', icon: '' }
    // },
    // {
    //   path: 'group',
    //   component: () => import('@/views/user/group/index'),
    //   name: 'Group',
    //   meta: { title: '用户分组', icon: '' }
    // }
  ]
}

export default appRouter
