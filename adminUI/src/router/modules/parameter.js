import Layout from "@/layout";

const ParameterRouter = {
  path: "/parameter",
  component: Layout,
  redirect: "/parameter/index",
  name: "Parameter",
  meta: {
    title: "paramSettings",
    icon: ""
  },
  children: [
    {
      path: "reward-rules",
      component: () => import("@/views/parameter/reward-rules/index"),
      name: "ParameterRewardRules",
      meta: { title: "rewardRules", icon: "" }
    },

    {
      path: "shoppingCashbackRules",
      component: () => import("@/views/parameter/shopping-cashback-rules/index"),
      name: "shoppingCashbackRules",
      meta: { title: "shoppingCashbackRules", icon: "" }
    },

    {
      path: "platformCashbackRate",
      component: () => import("@/views/parameter/platform-cashback-rate/index"),
      name: "platformCashbackRate",
      meta: { title: "platformCashbackRate", icon: "" }
    },
    {
      path: "withdrawal-fees",
      component: () => import("@/views/parameter/withdrawal-fees/index"),
      name: "WithdrawalFees",
      meta: { title: "withdrawalFee", icon: "" }
    },
    {
      path: "membership-upgrade",
      component: () => import("@/views/parameter/membership-upgrade/index"),
      name: "MembershipUpgrade",
      meta: { title: "membershipFee", icon: "" }
    },
    {
      path: "referral-reward-config",
      component: () => import("@/views/parameter/referral-reward-config/index"),
      name: "ReferralRewardConfig",
      meta: { title: "referralRewardConfig", icon: "" }
    }
    // {
    //   path: 'group',
    //   component: () => import('@/views/user/group/index'),
    //   name: 'Group',
    //   meta: { title: '用户分组', icon: '' }
    // }
  ]
};

export default ParameterRouter;
