# Common variables and functions for Makefile

# Project information - dynamically get absolute paths
# Get the directory where the main Makefile is located
ROOT_DIR := $(shell dirname $(realpath $(firstword $(MAKEFILE_LIST))))
PROJECT_NAME := $(shell basename $(ROOT_DIR))
API_DIR := $(ROOT_DIR)/api
ADMIN_UI_DIR := $(ROOT_DIR)/adminUI
APP_UI_DIR := $(ROOT_DIR)/appUI

# Maven settings
MAVEN_OPTS := -Dmaven.test.skip=true
MAVEN_PROFILES := dev

# Docker settings
DOCKER_REGISTRY := 
DOCKER_TAG := latest

# Colors for output
RED := \033[31m
GREEN := \033[32m
YELLOW := \033[33m
BLUE := \033[34m
RESET := \033[0m

# Helper functions
define log_info
	@echo "$(BLUE)[INFO]$(RESET) $(1)"
endef

define log_success
	@echo "$(GREEN)[SUCCESS]$(RESET) $(1)"
endef

define log_warning
	@echo "$(YELLOW)[WARNING]$(RESET) $(1)"
endef

define log_error
	@echo "$(RED)[ERROR]$(RESET) $(1)"
endef

# Check if command exists
define check_command
	@which $(1) > /dev/null || (echo "$(RED)[ERROR]$(RESET) $(1) is not installed" && exit 1)
endef

# Common rules - naming convention: common.<module>.<command>
.PHONY: common.logs.admin common.logs.front common.logs.admin-ui \
        common.port.admin common.port.front common.port.admin-ui \
        common.status.all common.status.admin common.status.front common.status.admin-ui \
        show-ports

# Service identification patterns
ADMIN_SERVICE_PATTERN := GencoAdminApplication
FRONT_SERVICE_PATTERN := GencoFrontApplication
ADMIN_UI_SERVICE_PATTERN := vue-cli-service serve

# Log file paths
ADMIN_LOG := logs/genco-admin.log
FRONT_LOG := logs/genco-front.log
ADMIN_UI_LOG := logs/admin-ui.log

# Directory paths
ADMIN_DIR := $(API_DIR)/genco-admin
FRONT_DIR := $(API_DIR)/genco-front
MAVEN_PROFILE := dev

# Functions to get actual running ports by PID
define get-admin-port
$(shell PID=$$(ps aux | grep -v grep | grep "$(ADMIN_SERVICE_PATTERN)" | awk '{print $$2}' | head -1); \
if [ -n "$$PID" ]; then \
	lsof -Pan -p $$PID -i 2>/dev/null | grep LISTEN | awk -F: '{print $$2}' | awk '{print $$1}' | head -1; \
else \
	grep -A 5 "^server:" $(ADMIN_DIR)/src/main/resources/application-$(MAVEN_PROFILE).yml 2>/dev/null | grep "port:" | head -1 | awk '{print $$2}' || echo "8080"; \
fi)
endef

define get-front-port
$(shell PID=$$(ps aux | grep -v grep | grep "$(FRONT_SERVICE_PATTERN)" | awk '{print $$2}' | head -1); \
if [ -n "$$PID" ]; then \
	lsof -Pan -p $$PID -i 2>/dev/null | grep LISTEN | awk -F: '{print $$2}' | awk '{print $$1}' | head -1; \
else \
	grep -A 5 "^server:" $(FRONT_DIR)/src/main/resources/application-$(MAVEN_PROFILE).yml 2>/dev/null | grep "port:" | head -1 | awk '{print $$2}' || echo "8081"; \
fi)
endef

define get-admin-ui-port
$(shell PID=$$(ps aux | grep -v grep | grep "$(ADMIN_UI_SERVICE_PATTERN)" | awk '{print $$2}' | head -1); \
if [ -n "$$PID" ]; then \
	lsof -Pan -p $$PID -i 2>/dev/null | grep LISTEN | awk -F: '{print $$2}' | awk '{print $$1}' | head -1; \
else \
	grep "const port" $(ADMIN_UI_DIR)/vue.config.js 2>/dev/null | awk -F'||' '{print $$NF}' | awk '{print $$1}' || echo "9527"; \
fi)
endef

# Common log commands
common.logs.admin:
	$(call log_info,"Showing genco-admin logs...")
	@if [ -f $(ROOT_DIR)/$(ADMIN_LOG) ]; then \
		tail -f $(ROOT_DIR)/$(ADMIN_LOG); \
	else \
		echo "$(RED)Log file not found: $(ADMIN_LOG)$(RESET)"; \
	fi

common.logs.front:
	$(call log_info,"Showing genco-front logs...")
	@if [ -f $(ROOT_DIR)/$(FRONT_LOG) ]; then \
		tail -f $(ROOT_DIR)/$(FRONT_LOG); \
	else \
		echo "$(RED)Log file not found: $(FRONT_LOG)$(RESET)"; \
	fi

common.logs.admin-ui:
	$(call log_info,"Showing adminUI logs...")
	@if [ -f $(ROOT_DIR)/$(ADMIN_UI_LOG) ]; then \
		tail -f $(ROOT_DIR)/$(ADMIN_UI_LOG); \
	else \
		echo "$(RED)Log file not found: $(ADMIN_UI_LOG)$(RESET)"; \
	fi

# Common port commands
common.port.admin:
	@echo "$(call get-admin-port)"

common.port.front:
	@echo "$(call get-front-port)"

common.port.admin-ui:
	@echo "$(call get-admin-ui-port)"

# Common status commands
common.status.all:
	$(call log_info,"Checking service status...")
	@echo ""
	@echo "Service Status:"
	@ADMIN_PID=$$(ps aux | grep -v grep | grep "$(ADMIN_SERVICE_PATTERN)" | awk '{print $$2}' | head -1); \
	FRONT_PID=$$(ps aux | grep -v grep | grep "$(FRONT_SERVICE_PATTERN)" | awk '{print $$2}' | head -1); \
	ADMIN_UI_PID=$$(ps aux | grep -v grep | grep "$(ADMIN_UI_SERVICE_PATTERN)" | awk '{print $$2}' | head -1); \
	if [ -n "$$ADMIN_PID" ]; then \
		echo "  genco-admin:  $(GREEN)Running$(RESET) (PID: $$ADMIN_PID) - http://localhost:$(call get-admin-port)"; \
	else \
		echo "  genco-admin:  $(RED)Stopped$(RESET)"; \
	fi; \
	if [ -n "$$FRONT_PID" ]; then \
		echo "  genco-front:  $(GREEN)Running$(RESET) (PID: $$FRONT_PID) - http://localhost:$(call get-front-port)"; \
	else \
		echo "  genco-front:  $(RED)Stopped$(RESET)"; \
	fi; \
	if [ -n "$$ADMIN_UI_PID" ]; then \
		echo "  adminUI:      $(GREEN)Running$(RESET) (PID: $$ADMIN_UI_PID) - http://localhost:$(call get-admin-ui-port)"; \
	else \
		echo "  adminUI:      $(RED)Stopped$(RESET)"; \
	fi
	@echo ""

show-ports:
	$(call log_info,"Detected port configurations:")
	@echo "  ADMIN_PORT: $(call get-admin-port)"
	@echo "  FRONT_PORT: $(call get-front-port)"
	@echo "  ADMIN_UI_PORT: $(call get-admin-ui-port)"


