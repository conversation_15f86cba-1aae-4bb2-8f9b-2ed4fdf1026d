# Deployment rules for the project

.PHONY: deploy deploy-dev deploy-prod docker-build docker-push deploy-docker

# Deploy to development environment
deploy-dev: build
	$(call log_info,"Deploying to development environment...")
	@echo "Stopping existing services..."
	-docker-compose -f $(API_DIR)/docker-compose.yaml down
	@echo "Starting services..."
	docker-compose -f $(API_DIR)/docker-compose.yaml up -d
	$(call log_success,"Deployed to development environment")

# Deploy to production environment
deploy-prod: build
	$(call log_info,"Deploying to production environment...")
	$(call log_warning,"This will deploy to production. Are you sure? (y/N)")
	@read -p "" confirm; \
	if [ "$$confirm" = "y" ] || [ "$$confirm" = "Y" ]; then \
		echo "Proceeding with production deployment..."; \
		cd $(API_DIR) && ./build-and-push.sh; \
	else \
		echo "Deployment cancelled."; \
		exit 1; \
	fi
	$(call log_success,"Deployed to production environment")

# Build Docker images
docker-build:
	$(call log_info,"Building Docker images...")
	cd $(API_DIR) && docker build -t $(PROJECT_NAME)-api:$(DOCKER_TAG) .
	cd $(ADMIN_UI_DIR) && docker build -t $(PROJECT_NAME)-admin:$(DOCKER_TAG) .
	$(call log_success,"Docker images built")

# Push Docker images to registry
docker-push: docker-build
	$(call log_info,"Pushing Docker images to registry...")
	@if [ -z "$(DOCKER_REGISTRY)" ]; then \
		echo "$(RED)DOCKER_REGISTRY not set$(RESET)"; \
		exit 1; \
	fi
	docker tag $(PROJECT_NAME)-api:$(DOCKER_TAG) $(DOCKER_REGISTRY)/$(PROJECT_NAME)-api:$(DOCKER_TAG)
	docker tag $(PROJECT_NAME)-admin:$(DOCKER_TAG) $(DOCKER_REGISTRY)/$(PROJECT_NAME)-admin:$(DOCKER_TAG)
	docker push $(DOCKER_REGISTRY)/$(PROJECT_NAME)-api:$(DOCKER_TAG)
	docker push $(DOCKER_REGISTRY)/$(PROJECT_NAME)-admin:$(DOCKER_TAG)
	$(call log_success,"Docker images pushed to registry")

# Deploy using Docker
deploy-docker: docker-build
	$(call log_info,"Deploying with Docker...")
	docker-compose -f $(API_DIR)/docker-compose.yaml down
	docker-compose -f $(API_DIR)/docker-compose.yaml up -d
	$(call log_success,"Docker deployment completed")

# Legacy start command - now redirects to service.all.start (defined in service.mk)
start:
	$(call log_info,"Redirecting to service.all.start for better service management...")
	@echo "$(YELLOW)Note: Use 'make service.all.start' or individual commands like 'make service.admin.start' for better control$(RESET)"
	@$(MAKE) service.all.start

# Legacy stop command - now redirects to service.all.stop (defined in service.mk)
stop:
	$(call log_info,"Redirecting to service.all.stop for better service management...")
	@echo "$(YELLOW)Note: Use 'make service.all.stop' or individual commands like 'make service.admin.stop' for better control$(RESET)"
	@$(MAKE) service.all.stop

# Legacy restart command - now redirects to service.all.restart (defined in service.mk)
restart:
	$(call log_info,"Redirecting to service.all.restart for better service management...")
	@echo "$(YELLOW)Note: Use 'make service.all.restart' or individual commands like 'make service.admin.restart' for better control$(RESET)"
	@$(MAKE) service.all.restart

# Health check
health-check:
	$(call log_info,"Performing health check...")
	@echo "Checking API health..."
	@curl -f http://localhost:8080/actuator/health || echo "API health check failed"
	@echo "Checking Admin UI..."
	@curl -f http://localhost:8081 || echo "Admin UI health check failed"
	$(call log_success,"Health check completed")
