# Start rules for the project components

# Service rules - naming convention: service.<module>.<command>
.PHONY: service.admin.start service.admin.stop service.admin.restart \
        service.front.start service.front.stop service.front.restart \
        service.admin-ui.start service.admin-ui.stop service.admin-ui.restart \
        service.all.start service.all.stop service.all.restart

# Log directories
LOG_DIR := logs

# Create necessary directories
create-log-dir:
	@mkdir -p $(LOG_DIR)

# ============================================================================
# Standardized Commands with Naming Convention: <module>::<submodule>::<command>
# ============================================================================

# Admin Module Commands
service.admin.start: create-log-dir
	$(call log_info,"Starting genco-admin service...")
	$(call check_command,mvn)
	@EXISTING_PID=$$(ps aux | grep -v grep | grep "$(ADMIN_SERVICE_PATTERN)" | awk '{print $$2}' | head -1); \
	if [ -n "$$EXISTING_PID" ]; then \
		echo "$(YELLOW)genco-admin is already running (PID: $$EXISTING_PID)$(RESET)"; \
		echo "Admin API is available at: http://localhost:$(call get-admin-port)"; \
	else \
		echo "Starting genco-admin in background..."; \
		cd $(ADMIN_DIR) && nohup mvn spring-boot:run -Dspring-boot.run.profiles=$(MAVEN_PROFILE) \
			> $(ROOT_DIR)/$(ADMIN_LOG) 2>&1 & \
		echo "$(GREEN)genco-admin started$(RESET)"; \
		echo "Log file: $(ADMIN_LOG)"; \
		echo "Waiting for startup..."; \
		sleep 5; \
		NEW_PID=$$(ps aux | grep -v grep | grep "$(ADMIN_SERVICE_PATTERN)" | awk '{print $$2}' | head -1); \
		if [ -n "$$NEW_PID" ]; then \
			echo "$(GREEN)genco-admin is running successfully$(RESET) (PID: $$NEW_PID)"; \
			echo "Admin API will be available at: http://localhost:$(call get-admin-port)"; \
			echo "API Documentation: http://localhost:$(call get-admin-port)/doc.html"; \
		else \
			echo "$(RED)genco-admin failed to start$(RESET)"; \
			echo "Check log file: $(ADMIN_LOG)"; \
		fi; \
	fi

service.admin.stop:
	$(call log_info,"Stopping genco-admin service...")
	@EXISTING_PID=$$(ps aux | grep -v grep | grep "$(ADMIN_SERVICE_PATTERN)" | awk '{print $$2}' | head -1); \
	if [ -n "$$EXISTING_PID" ]; then \
		kill $$EXISTING_PID && echo "$(GREEN)genco-admin stopped$(RESET) (PID: $$EXISTING_PID)"; \
	else \
		echo "$(YELLOW)genco-admin is not running$(RESET)"; \
	fi

service.admin.restart: service.admin.stop service.admin.start
	$(call log_success,"genco-admin restarted")



# Front Module Commands
service.front.start: create-log-dir
	$(call log_info,"Starting genco-front service...")
	$(call check_command,mvn)
	@EXISTING_PID=$$(ps aux | grep -v grep | grep "$(FRONT_SERVICE_PATTERN)" | awk '{print $$2}' | head -1); \
	if [ -n "$$EXISTING_PID" ]; then \
		echo "$(YELLOW)genco-front is already running (PID: $$EXISTING_PID)$(RESET)"; \
		echo "Front API is available at: http://localhost:$(call get-front-port)"; \
	else \
		echo "Starting genco-front in background..."; \
		cd $(FRONT_DIR) && nohup mvn spring-boot:run -Dspring-boot.run.profiles=$(MAVEN_PROFILE) \
			> $(ROOT_DIR)/$(FRONT_LOG) 2>&1 & \
		echo "$(GREEN)genco-front started$(RESET)"; \
		echo "Log file: $(FRONT_LOG)"; \
		echo "Waiting for startup..."; \
		sleep 5; \
		NEW_PID=$$(ps aux | grep -v grep | grep "$(FRONT_SERVICE_PATTERN)" | awk '{print $$2}' | head -1); \
		if [ -n "$$NEW_PID" ]; then \
			echo "$(GREEN)genco-front is running successfully$(RESET) (PID: $$NEW_PID)"; \
			echo "Front API will be available at: http://localhost:$(call get-front-port)"; \
			echo "API Documentation: http://localhost:$(call get-front-port)/doc.html"; \
		else \
			echo "$(RED)genco-front failed to start$(RESET)"; \
			echo "Check log file: $(FRONT_LOG)"; \
		fi; \
	fi

service.front.stop:
	$(call log_info,"Stopping genco-front service...")
	@EXISTING_PID=$$(ps aux | grep -v grep | grep "$(FRONT_SERVICE_PATTERN)" | awk '{print $$2}' | head -1); \
	if [ -n "$$EXISTING_PID" ]; then \
		kill $$EXISTING_PID && echo "$(GREEN)genco-front stopped$(RESET) (PID: $$EXISTING_PID)"; \
	else \
		echo "$(YELLOW)genco-front is not running$(RESET)"; \
	fi

service.front.restart: service.front.stop service.front.start
	$(call log_success,"genco-front restarted")



# Admin-UI Module Commands
service.admin-ui.start: create-log-dir
	$(call log_info,"Starting adminUI service...")
	$(call check_command,npm)
	@EXISTING_PID=$$(ps aux | grep -v grep | grep "$(ADMIN_UI_SERVICE_PATTERN)" | awk '{print $$2}' | head -1); \
	if [ -n "$$EXISTING_PID" ]; then \
		echo "$(YELLOW)adminUI is already running (PID: $$EXISTING_PID)$(RESET)"; \
		echo "Admin UI is available at: http://localhost:$(call get-admin-ui-port)"; \
	else \
		echo "Installing dependencies..."; \
		cd $(ADMIN_UI_DIR) && npm install > /dev/null 2>&1; \
		echo "Starting adminUI in background..."; \
		cd $(ADMIN_UI_DIR) && nohup npm run devs > $(ROOT_DIR)/$(ADMIN_UI_LOG) 2>&1 & \
		echo "$(GREEN)adminUI started$(RESET)"; \
		echo "Log file: $(ADMIN_UI_LOG)"; \
		echo "Waiting for startup..."; \
		sleep 10; \
		NEW_PID=$$(ps aux | grep -v grep | grep "$(ADMIN_UI_SERVICE_PATTERN)" | awk '{print $$2}' | head -1); \
		if [ -n "$$NEW_PID" ]; then \
			echo "$(GREEN)adminUI is running successfully$(RESET) (PID: $$NEW_PID)"; \
			echo "Admin UI will be available at: http://localhost:$(call get-admin-ui-port)"; \
		else \
			echo "$(RED)adminUI failed to start$(RESET)"; \
			echo "Check log file: $(ADMIN_UI_LOG)"; \
		fi; \
	fi

service.admin-ui.stop:
	$(call log_info,"Stopping adminUI service...")
	@EXISTING_PID=$$(ps aux | grep -v grep | grep "$(ADMIN_UI_SERVICE_PATTERN)" | awk '{print $$2}' | head -1); \
	if [ -n "$$EXISTING_PID" ]; then \
		kill $$EXISTING_PID && echo "$(GREEN)adminUI stopped$(RESET) (PID: $$EXISTING_PID)"; \
	else \
		echo "$(YELLOW)adminUI is not running$(RESET)"; \
	fi

service.admin-ui.restart: service.admin-ui.stop service.admin-ui.start
	$(call log_success,"adminUI restarted")



# ============================================================================
# All Module Commands (Batch Operations)
# ============================================================================

service.all.start: service.admin.start service.front.start service.admin-ui.start
	$(call log_success,"All services started successfully!")
	@echo ""
	@echo "$(BLUE)Service URLs:$(RESET)"
	@echo "  Admin API:    http://localhost:$(call get-admin-port)"
	@echo "  Front API:    http://localhost:$(call get-front-port)"
	@echo "  Admin UI:     http://localhost:$(call get-admin-ui-port)"
	@echo ""
	@echo "$(BLUE)API Documentation:$(RESET)"
	@echo "  Admin API:    http://localhost:$(call get-admin-port)/doc.html"
	@echo "  Front API:    http://localhost:$(call get-front-port)/doc.html"
	@echo ""
	@echo "$(BLUE)Log files:$(RESET)"
	@echo "  Admin API:    $(ADMIN_LOG)"
	@echo "  Front API:    $(FRONT_LOG)"
	@echo "  Admin UI:     $(ADMIN_UI_LOG)"

service.all.stop: service.admin.stop service.front.stop service.admin-ui.stop
	$(call log_success,"All services stopped")

service.all.restart: service.all.stop service.all.start
	$(call log_success,"All services restarted")
	$(call log_info,"Checking service status...")
	@echo ""
	@echo "$(BLUE)Service Status:$(RESET)"
	@ADMIN_PID=$$(ps aux | grep -v grep | grep "$(ADMIN_SERVICE_PATTERN)" | awk '{print $$2}' | head -1); \
	if [ -n "$$ADMIN_PID" ]; then \
		echo "  genco-admin:  $(GREEN)Running$(RESET) (PID: $$ADMIN_PID) - http://localhost:$(call get-admin-port)"; \
	else \
		echo "  genco-admin:  $(RED)Stopped$(RESET)"; \
	fi
	@FRONT_PID=$$(ps aux | grep -v grep | grep "$(FRONT_SERVICE_PATTERN)" | awk '{print $$2}' | head -1); \
	if [ -n "$$FRONT_PID" ]; then \
		echo "  genco-front:  $(GREEN)Running$(RESET) (PID: $$FRONT_PID) - http://localhost:$(call get-front-port)"; \
	else \
		echo "  genco-front:  $(RED)Stopped$(RESET)"; \
	fi
	@ADMIN_UI_PID=$$(ps aux | grep -v grep | grep "$(ADMIN_UI_SERVICE_PATTERN)" | awk '{print $$2}' | head -1); \
	if [ -n "$$ADMIN_UI_PID" ]; then \
		echo "  adminUI:      $(GREEN)Running$(RESET) (PID: $$ADMIN_UI_PID) - http://localhost:$(call get-admin-ui-port)"; \
	else \
		echo "  adminUI:      $(RED)Stopped$(RESET)"; \
	fi



# ============================================================================
# Additional Utility Commands
# ============================================================================

# Test port configuration (for debugging)
test-ports:
	@echo "$(BLUE)Port Configuration Test:$(RESET)"
	@echo "  MAVEN_PROFILE: $(MAVEN_PROFILE)"
	@echo "  Admin config file: $(ADMIN_DIR)/src/main/resources/application-$(MAVEN_PROFILE).yml"
	@echo "  Front config file: $(FRONT_DIR)/src/main/resources/application-$(MAVEN_PROFILE).yml"
	@echo "  Vue config file: $(ADMIN_UI_DIR)/vue.config.js"
	@echo ""
	@echo "$(BLUE)Manual Test:$(RESET)"
	@echo "  Admin port command: grep -A 5 \"^server:\" $(ADMIN_DIR)/src/main/resources/application-$(MAVEN_PROFILE).yml | grep \"port:\" | head -1 | awk '{print \$$2}'"
	@grep -A 5 "^server:" $(ADMIN_DIR)/src/main/resources/application-$(MAVEN_PROFILE).yml | grep "port:" | head -1 | awk '{print $$2}'
	@echo ""
	@echo "$(BLUE)Detected Ports:$(RESET)"
	@echo "  ADMIN_PORT: $(call get-admin-port)"
	@echo "  FRONT_PORT: $(call get-front-port)"
	@echo "  ADMIN_UI_PORT: $(call get-admin-ui-port)"
