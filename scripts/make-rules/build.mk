# Build rules for the project

.PHONY: build build-api build-admin-ui build-app-ui clean

# Build all components
build: build-api build-admin-ui
	$(call log_success,"All components built successfully")

# Build API (Spring Boot)
build-api:
	$(call log_info,"Building API...")
	$(call check_command,mvn)
	cd $(API_DIR) && mvn clean package $(MAVEN_OPTS) -P$(MAVEN_PROFILES)
	$(call log_success,"API built successfully")

# Build Admin UI (Vue.js)
build-admin-ui:
	$(call log_info,"Building Admin UI...")
	$(call check_command,npm)
	cd $(ADMIN_UI_DIR) && npm install && npm run build:prods
	$(call log_success,"Admin UI built successfully")

# Build App UI (Flutter) - optional
build-app-ui:
	$(call log_info,"Building App UI...")
	$(call check_command,flutter)
	cd $(APP_UI_DIR) && flutter pub get && flutter build apk
	$(call log_success,"App UI built successfully")

# Clean build artifacts
clean:
	$(call log_info,"Cleaning build artifacts...")
	cd $(API_DIR) && mvn clean
	cd $(ADMIN_UI_DIR) && rm -rf dist node_modules/.cache
	cd $(APP_UI_DIR) && flutter clean
	$(call log_success,"Clean completed")

# Install dependencies
install-deps:
	$(call log_info,"Installing dependencies...")
	cd $(API_DIR) && mvn dependency:resolve
	cd $(ADMIN_UI_DIR) && npm install
	cd $(APP_UI_DIR) && flutter pub get
	$(call log_success,"Dependencies installed")

# Compile only (no packaging)
compile:
	$(call log_info,"Compiling API...")
	cd $(API_DIR) && mvn compile
	$(call log_success,"Compilation completed")
