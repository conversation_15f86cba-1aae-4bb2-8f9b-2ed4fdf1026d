# Test rules for the project

.PHONY: test test-api test-admin-ui test-integration test-referral-reward

# Run all tests
test: test-api test-admin-ui
	$(call log_success,"All tests completed")

# Test API (Spring Boot)
test-api:
	$(call log_info,"Running API tests...")
	cd $(API_DIR) && mvn test
	$(call log_success,"API tests completed")

# Test Admin UI (Vue.js)
test-admin-ui:
	$(call log_info,"Running Admin UI tests...")
	cd $(ADMIN_UI_DIR) && npm test
	$(call log_success,"Admin UI tests completed")

# Integration tests
test-integration:
	$(call log_info,"Running integration tests...")
	cd $(API_DIR) && mvn verify -Pintegration-test
	$(call log_success,"Integration tests completed")


# Quick test of referral reward endpoints (simple version)
test-referral-reward-quick: check-auth-token
	$(call log_info,"Quick testing referral reward endpoints...")
	@echo "Testing user center endpoint..."
	@curl -s -H "Authorization: Bearer $(AUTH_TOKEN)" \
		-H "Content-Type: application/json" \
		"http://localhost:8080/api/front/user" | jq '.'

	@echo "Testing referral reward config endpoint..."
	@curl -s -H "Authorization: Bearer $(AUTH_TOKEN)" \
		-H "Content-Type: application/json" \
		"http://localhost:8080/api/front/user/referral-reward-config" | jq '.'

	@echo "Testing referral reward config update..."
	@curl -s -X POST -H "Authorization: Bearer $(AUTH_TOKEN)" \
		-H "Content-Type: application/json" \
		-d '{"pullNewTarget":15,"firstOrderTarget":8,"rewardAmount":150000,"status":1}' \
		"http://localhost:8080/api/front/user/referral-reward-config" | jq '.'

	$(call log_success,"Quick referral reward tests completed")

# Simulate user login and get token
login-test:
	$(call log_info,"Simulating user login...")
	@echo "Please provide login credentials:"
	@read -p "Phone: " phone; \
	read -s -p "Password: " password; \
	echo; \
	response=$$(curl -s -X POST -H "Content-Type: application/json" \
		-d "{\"account\":\"$$phone\",\"password\":\"$$password\"}" \
		"http://localhost:8080/api/front/login"); \
	token=$$(echo $$response | jq -r '.data.token // empty'); \
	if [ -n "$$token" ]; then \
		echo "$$token" > .auth_token; \
		echo "$(GREEN)Login successful! Token saved to .auth_token$(RESET)"; \
	else \
		echo "$(RED)Login failed: $$response$(RESET)"; \
		exit 1; \
	fi

# Check if auth token exists
check-auth-token:
	@if [ ! -f .auth_token ]; then \
		echo "$(RED)No auth token found. Please run 'make login-test' first$(RESET)"; \
		exit 1; \
	fi
	$(eval AUTH_TOKEN := $(shell cat .auth_token))

# Performance test
test-performance:
	$(call log_info,"Running performance tests...")
	@echo "Testing API performance with ab (Apache Bench)..."
	$(call check_command,ab)
	ab -n 100 -c 10 http://localhost:8080/api/front/user
	$(call log_success,"Performance tests completed")
